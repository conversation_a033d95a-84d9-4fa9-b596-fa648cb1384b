package app

import (
	"context"
	"net/http"
	"strconv"
	"time"

	"git.uozi.org/uozi/potato-billing-api/api"
	"git.uozi.org/uozi/potato-billing-api/internal/billing"
	"git.uozi.org/uozi/potato-billing-api/model"
	"git.uozi.org/uozi/potato-billing-api/query"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/logger"
)

// CreateApplicationRequest 创建应用请求
type CreateApplicationRequest struct {
	Name    string `json:"name" binding:"required"`
	Comment string `json:"comment"`
}

// UpdateApplicationRequest 更新应用请求
type UpdateApplicationRequest struct {
	Name    string `json:"name"`
	Comment string `json:"comment"`
}

// AppStatsData 应用统计数据
type AppStatsData struct {
	TodayCalls   int64   `json:"today_calls"`   // 今日调用次数
	TodayCost    float64 `json:"today_cost"`    // 今日费用
	MonthlyCalls int64   `json:"monthly_calls"` // 本月调用次数
	MonthlyCost  float64 `json:"monthly_cost"`  // 本月费用
}

// AppWithStats 带统计数据的应用
type AppWithStats struct {
	*model.App
	Stats AppStatsData `json:"stats"`
}

// AppListResponse 应用列表响应
type AppListResponse struct {
	Data       []AppWithStats `json:"data"`
	Pagination interface{}    `json:"pagination"`
}

// getAppStats 获取单个应用的统计数据
func getAppStats(ctx context.Context, appID uint64) AppStatsData {
	db := cosy.UseDB(ctx)
	now := time.Now()
	
	// 今日开始时间
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	todayStartMs := todayStart.UnixMilli()
	nowMs := now.UnixMilli()
	
	// 本月开始时间
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	monthStartMs := monthStart.UnixMilli()
	
	stats := AppStatsData{}
	
	// 查询今日统计
	var todayResult struct {
		TotalCalls int64
		TotalCost  float64
	}
	
	err := db.Model(&model.UsageLog{}).
		Select("COUNT(*) as total_calls, COALESCE(SUM(cost), 0) as total_cost").
		Where("app_id = ? AND created_at >= ? AND created_at <= ?", appID, todayStartMs, nowMs).
		Scan(&todayResult).Error
	
	if err != nil {
		logger.Error("获取今日统计失败", "app_id", appID, "error", err)
	} else {
		stats.TodayCalls = todayResult.TotalCalls
		stats.TodayCost = todayResult.TotalCost
	}
	
	// 查询本月统计
	var monthlyResult struct {
		TotalCalls int64
		TotalCost  float64
	}
	
	err = db.Model(&model.UsageLog{}).
		Select("COUNT(*) as total_calls, COALESCE(SUM(cost), 0) as total_cost").
		Where("app_id = ? AND created_at >= ? AND created_at <= ?", appID, monthStartMs, nowMs).
		Scan(&monthlyResult).Error
	
	if err != nil {
		logger.Error("获取本月统计失败", "app_id", appID, "error", err)
	} else {
		stats.MonthlyCalls = monthlyResult.TotalCalls
		stats.MonthlyCost = monthlyResult.TotalCost
	}
	
	return stats
}

// GetApplicationList 获取应用列表
func GetApplicationList(c *gin.Context) {
	user := api.CurrentUser(c)

	// 获取查询参数
	search := c.Query("search")
	status := c.Query("status")
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("pageSize", "20")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	// 构建查询
	db := cosy.UseDB(c.Request.Context())
	q := query.Use(db).App

	queryBuilder := q.Where(q.UserID.Eq(user.ID))

	// 添加搜索条件
	if search != "" {
		queryBuilder = queryBuilder.Where(
			q.Name.Like("%"+search+"%"),
		).Or(
			q.Comment.Like("%"+search+"%"),
		)
	}

	// 添加状态过滤
	if status != "" {
		queryBuilder = queryBuilder.Where(q.Status.Eq(status))
	}

	// 获取总数
	total, err := queryBuilder.Count()
	if err != nil {
		logger.Error("获取应用总数失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取应用列表失败"})
		return
	}

	// 分页查询
	offset := (page - 1) * pageSize
	apps, err := queryBuilder.
		Preload(q.User).
		Order(q.CreatedAt.Desc()).
		Limit(pageSize).
		Offset(offset).
		Find()
	if err != nil {
		logger.Error("获取应用列表失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取应用列表失败"})
		return
	}

	// 为每个应用添加统计数据
	appsWithStats := make([]AppWithStats, 0, len(apps))
	for _, app := range apps {
		stats := getAppStats(c.Request.Context(), app.ID)
		appWithStats := AppWithStats{
			App:   app,
			Stats: stats,
		}
		appsWithStats = append(appsWithStats, appWithStats)
	}

	// 构建响应
	response := AppListResponse{
		Data: appsWithStats,
		Pagination: map[string]interface{}{
			"total":        total,
			"per_page":     pageSize,
			"current_page": page,
			"total_pages":  (total + int64(pageSize) - 1) / int64(pageSize),
		},
	}

	c.JSON(http.StatusOK, response)
}

// GetApplication 获取应用详情
func GetApplication(c *gin.Context) {
	user := api.CurrentUser(c)
	idStr := c.Param("id")

	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	app, err := query.App.
		Preload(query.App.User).
		Where(query.App.ID.Eq(id), query.App.UserID.Eq(user.ID)).
		First()
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, app)
}

// CreateApplication 创建应用
func CreateApplication(c *gin.Context) {
	user := api.CurrentUser(c)

	var req CreateApplicationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 创建新的API Key记录作为应用
	app := &model.App{
		Name:    req.Name,
		Status:  "ok",
		UserID:  user.ID,
		Comment: req.Comment,
	}

	billingService := billing.GetBillingService()
	err := billingService.GetAppService().CreateApp(c.Request.Context(), app)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 重新查询以获取完整信息
	result, err := query.App.
		Preload(query.App.User).
		Where(query.App.ID.Eq(app.ID)).
		First()
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, result)
}

// UpdateApplication 更新应用
func UpdateApplication(c *gin.Context) {
	user := api.CurrentUser(c)
	idStr := c.Param("id")

	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	var req UpdateApplicationRequest
	if bindErr := c.ShouldBindJSON(&req); bindErr != nil {
		cosy.ErrHandler(c, bindErr)
		return
	}

	// 检查应用是否属于当前用户
	_, err = query.App.
		Where(query.App.ID.Eq(id), query.App.UserID.Eq(user.ID)).
		First()
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 更新应用信息
	updates := make(map[string]interface{})
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Comment != "" {
		updates["comment"] = req.Comment
	}

	_, err = query.App.
		Where(query.App.ID.Eq(id), query.App.UserID.Eq(user.ID)).
		Updates(updates)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 返回更新后的数据
	result, err := query.App.
		Preload(query.App.User).
		Where(query.App.ID.Eq(id)).
		First()
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, result)
}

// DeleteApplication 删除应用
func DeleteApplication(c *gin.Context) {
	user := api.CurrentUser(c)
	idStr := c.Param("id")

	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 检查应用是否属于当前用户
	_, err = query.App.
		Where(query.App.ID.Eq(id), query.App.UserID.Eq(user.ID)).
		First()
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 删除应用
	_, err = query.App.
		Where(query.App.ID.Eq(id), query.App.UserID.Eq(user.ID)).
		Delete()
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "应用删除成功"})
}

// RegenerateApiKey 重新生成API Key
func RegenerateApiKey(c *gin.Context) {
	user := api.CurrentUser(c)
	idStr := c.Param("id")

	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 检查应用是否属于当前用户
	_, err = query.App.
		Where(query.App.ID.Eq(id), query.App.UserID.Eq(user.ID)).
		First()
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	app := &model.App{}

	// 生成新的API Key
	newAPIKey := app.GenerateAPIKey()

	_, err = query.App.
		Where(query.App.ID.Eq(id), query.App.UserID.Eq(user.ID)).
		Update(query.App.APIKey, newAPIKey)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 返回更新后的数据
	result, err := query.App.
		Preload(query.App.User).
		Where(query.App.ID.Eq(id)).
		First()
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, result)
}
