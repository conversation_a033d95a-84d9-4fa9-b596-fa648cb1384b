package billing

import (
	"context"
	"fmt"
	"math"
	"net/http"
	"strconv"
	"time"

	"git.uozi.org/uozi/potato-billing-api/api"
	"git.uozi.org/uozi/potato-billing-api/internal/billing"
	"git.uozi.org/uozi/potato-billing-api/model"
	"git.uozi.org/uozi/potato-billing-api/query"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/logger"
)

// ModuleStatsResponse 模块统计响应结构
type ModuleStatsResponse struct {
	LLM ModuleStatItem `json:"llm"`
	TTS ModuleStatItem `json:"tts"`
	ASR ModuleStatItem `json:"asr"`
}

// ModuleStatItem 模块统计项
type ModuleStatItem struct {
	Calls          int64   `json:"calls"`
	Usage          int64   `json:"usage"`
	Cost           float64 `json:"cost"`
	AvgCostPerCall float64 `json:"avg_cost_per_call"`
}

// ModuleTrendResponse 模块趋势响应结构
type ModuleTrendResponse struct {
	Labels   []string             `json:"labels"`
	Datasets []ModuleTrendDataset `json:"datasets"`
}

// ModuleTrendDataset 模块趋势数据集
type ModuleTrendDataset struct {
	Label string    `json:"label"`
	Data  []float64 `json:"data"`
}

// GetModuleStats 获取按模块统计数据
func GetModuleStats(c *gin.Context) {
	// 获取当前用户
	currentUser := api.CurrentUser(c)
	if currentUser == nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未登录"})
		return
	}

	period := c.DefaultQuery("period", "30d")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")
	appID := cast.ToUint64(c.Query("app_id"))
	module := c.Query("module")

	// 如果指定了appID，验证该应用是否属于当前用户
	if appID != 0 {
		if !isUserApp(c.Request.Context(), currentUser.ID, appID) {
			c.JSON(http.StatusForbidden, gin.H{"error": "无权访问该应用数据"})
			return
		}
	}

	// 获取统计服务实例
	statService := billing.GetStatServiceInstance()
	if statService == nil {
		logger.Error("获取统计服务失败")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "统计服务不可用"})
		return
	}

	// 调用统计服务获取模块统计数据，限制为当前用户的数据
	stats, err := getModuleStatsForUser(c.Request.Context(), statService, period, startDate, endDate, currentUser.ID, appID, module)
	if err != nil {
		logger.Error("获取模块统计失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取模块统计数据失败"})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// GetModuleTrends 获取按模块趋势数据
func GetModuleTrends(c *gin.Context) {
	// 获取当前用户
	currentUser := api.CurrentUser(c)
	if currentUser == nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未登录"})
		return
	}

	period := c.DefaultQuery("period", "30d")
	metric := c.DefaultQuery("metric", "calls")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")
	appID := cast.ToUint64(c.Query("app_id"))

	// 如果指定了appID，验证该应用是否属于当前用户
	if appID != 0 {
		if !isUserApp(c.Request.Context(), currentUser.ID, appID) {
			c.JSON(http.StatusForbidden, gin.H{"error": "无权访问该应用数据"})
			return
		}
	}

	// 获取统计服务实例
	statService := billing.GetStatServiceInstance()
	if statService == nil {
		logger.Error("获取统计服务失败")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "统计服务不可用"})
		return
	}

	// 调用统计服务获取模块趋势数据，限制为当前用户的数据
	trends, err := getModuleTrendsForUser(c.Request.Context(), statService, period, metric, startDate, endDate, currentUser.ID, appID)
	if err != nil {
		logger.Error("获取模块趋势失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取模块趋势数据失败"})
		return
	}

	c.JSON(http.StatusOK, trends)
}

// ModuleUsage 模块使用量
type ModuleUsage struct {
	LLM int64 `json:"llm"`
	TTS int64 `json:"tts"`
	ASR int64 `json:"asr"`
}

// BillingStatsResponse 计费统计概览响应结构
type BillingStatsResponse struct {
	TotalCalls        int64       `json:"total_calls"`
	TotalUsage        ModuleUsage `json:"total_usage"`
	TotalCost         float64     `json:"total_cost"`
	AvgCostPerCall    float64     `json:"avg_cost_per_call"`
	AppCount          int64       `json:"app_count"`
	TodayCost         float64     `json:"today_cost"`
	TodayCostGrowth   float64     `json:"today_cost_growth"`
	MonthlyCost       float64     `json:"monthly_cost"`
	MonthlyCostGrowth float64     `json:"monthly_cost_growth"`
	MonthlyCalls      int64       `json:"monthly_calls"`
	DailyAverageCost  float64     `json:"daily_average_cost"`
}

// UsageTrendResponse 使用趋势响应结构
type UsageTrendResponse struct {
	Labels   []string    `json:"labels"`
	Datasets []TrendData `json:"datasets"`
}

// TrendData 趋势数据
type TrendData struct {
	Label string    `json:"label"`
	Data  []float64 `json:"data"`
}

// ServiceUsageResponse 服务使用分布响应结构
type ServiceUsageResponse struct {
	Labels   []string      `json:"labels"`
	Datasets []ServiceData `json:"datasets"`
}

// ServiceData 服务数据
type ServiceData struct {
	Data            []float64 `json:"data"`
	BackgroundColor []string  `json:"background_color"`
	BorderWidth     int       `json:"border_width"`
}

// BillingPeriodResponse 计费周期响应结构
type BillingPeriodResponse struct {
	ID        uint64  `json:"id"`
	Module    string  `json:"module"`
	Usage     int64   `json:"usage"`
	Cost      float64 `json:"cost"`
	Calls     int64   `json:"calls"`
	CreatedAt int64   `json:"created_at"`
}

// ApplicationBillingResponse 应用计费数据响应结构
type ApplicationBillingResponse struct {
	Stats    BillingStatsResponse   `json:"stats"`
	Trends   []UsageTrendResponse   `json:"trends"`
	Services []ServiceUsageResponse `json:"services"`
}

// GetBillingStats 获取计费统计概览
func GetBillingStats(c *gin.Context) {
	// 获取当前用户
	currentUser := api.CurrentUser(c)
	if currentUser == nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未登录"})
		return
	}

	period := c.DefaultQuery("period", "30d")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")
	appID := cast.ToUint64(c.Query("app_id"))

	// 如果指定了appID，验证该应用是否属于当前用户
	if appID != 0 {
		if !isUserApp(c.Request.Context(), currentUser.ID, appID) {
			c.JSON(http.StatusForbidden, gin.H{"error": "无权访问该应用数据"})
			return
		}
	}

	// 获取统计服务实例
	statService := billing.GetStatServiceInstance()
	if statService == nil {
		logger.Error("获取统计服务失败")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "统计服务不可用"})
		return
	}

	// 获取模块统计数据，限制为当前用户的数据
	moduleStats, err := getModuleStatsForUser(c.Request.Context(), statService, period, startDate, endDate, currentUser.ID, appID, "")
	if err != nil {
		logger.Error("获取模块统计数据失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取模块统计数据失败"})
		return
	}

	// 获取用户应用数量
	userApps, err := getUserApps(c.Request.Context(), currentUser.ID)
	if err != nil {
		logger.Error("获取用户应用失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取应用数据失败"})
		return
	}

	// 计算基础统计数据
	totalCalls := moduleStats.LLM.Calls + moduleStats.TTS.Calls + moduleStats.ASR.Calls
	totalCost := moduleStats.LLM.Cost + moduleStats.TTS.Cost + moduleStats.ASR.Cost

	var avgCostPerCall float64
	if totalCalls > 0 {
		avgCostPerCall = totalCost / float64(totalCalls)
	}

	// 计算今日费用和增长率
	todayCost, todayCostGrowth, err := calculateTodayCostAndGrowth(c.Request.Context(), statService, currentUser.ID, appID)
	if err != nil {
		logger.Error("计算今日费用失败", "error", err)
		// 不中断请求，使用默认值
		todayCost = 0
		todayCostGrowth = 0
	}

	// 计算月度费用和增长率
	monthlyCost, monthlyCostGrowth, err := calculateMonthlyCostAndGrowth(c.Request.Context(), statService, currentUser.ID, appID)
	if err != nil {
		logger.Error("计算月度费用失败", "error", err)
		// 不中断请求，使用默认值
		monthlyCost = 0
		monthlyCostGrowth = 0
	}

	// 计算日均费用
	dailyAverageCost, err := calculateDailyAverageCost(c.Request.Context(), statService, period, startDate, endDate, currentUser.ID, appID)
	if err != nil {
		logger.Error("计算日均费用失败", "error", err)
		// 不中断请求，使用默认值
		dailyAverageCost = 0
	}

	// 计算月度请求次数
	monthlyCalls, err := calculateMonthlyCalls(c.Request.Context(), statService, currentUser.ID, appID)
	if err != nil {
		logger.Error("计算月度请求次数失败", "error", err)
		// 不中断请求，使用默认值
		monthlyCalls = 0
	}

	// 构建完整的响应数据
	response := BillingStatsResponse{
		TotalCalls: totalCalls,
		TotalUsage: ModuleUsage{
			LLM: moduleStats.LLM.Calls,
			TTS: moduleStats.TTS.Calls,
			ASR: moduleStats.ASR.Calls,
		},
		TotalCost:         totalCost,
		AvgCostPerCall:    avgCostPerCall,
		AppCount:          int64(len(userApps)),
		TodayCost:         todayCost,
		TodayCostGrowth:   todayCostGrowth,
		MonthlyCost:       monthlyCost,
		MonthlyCostGrowth: monthlyCostGrowth,
		MonthlyCalls:      monthlyCalls,
		DailyAverageCost:  dailyAverageCost,
	}

	c.JSON(http.StatusOK, response)
}

// GetUsageTrends 获取使用趋势数据
func GetUsageTrends(c *gin.Context) {
	// 获取当前用户
	currentUser := api.CurrentUser(c)
	if currentUser == nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未登录"})
		return
	}

	period := c.DefaultQuery("period", "30d")
	metric := c.DefaultQuery("metric", "calls")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")
	appID := cast.ToUint64(c.Query("app_id"))

	// 如果指定了appID，验证该应用是否属于当前用户
	if appID != 0 {
		if !isUserApp(c.Request.Context(), currentUser.ID, appID) {
			c.JSON(http.StatusForbidden, gin.H{"error": "无权访问该应用数据"})
			return
		}
	}

	// 获取统计服务实例
	statService := billing.GetStatServiceInstance()
	if statService == nil {
		logger.Error("获取统计服务失败")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "统计服务不可用"})
		return
	}

	// 调用统计服务获取趋势数据，限制为当前用户的数据
	trends, err := getModuleTrendsForUser(c.Request.Context(), statService, period, metric, startDate, endDate, currentUser.ID, appID)
	logger.Info("获取趋势数据", "trends", trends)
	if err != nil {
		logger.Error("获取趋势数据失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取趋势数据失败"})
		return
	}

	// 转换为前端需要的格式
	response := UsageTrendResponse{
		Labels: trends.Labels,
		Datasets: []TrendData{
			{
				Label: getMetricLabel(metric),
				Data:  aggregateDatasets(trends.Datasets),
			},
		},
	}

	c.JSON(http.StatusOK, response)
}

// GetServiceUsage 获取服务使用分布
func GetServiceUsage(c *gin.Context) {
	// 获取当前用户
	currentUser := api.CurrentUser(c)
	if currentUser == nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未登录"})
		return
	}

	period := c.DefaultQuery("period", "30d")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")
	appID := cast.ToUint64(c.Query("app_id"))

	// 如果指定了appID，验证该应用是否属于当前用户
	if appID != 0 {
		if !isUserApp(c.Request.Context(), currentUser.ID, appID) {
			c.JSON(http.StatusForbidden, gin.H{"error": "无权访问该应用数据"})
			return
		}
	}

	// 获取统计服务实例
	statService := billing.GetStatServiceInstance()
	if statService == nil {
		logger.Error("获取统计服务失败")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "统计服务不可用"})
		return
	}

	// 获取模块统计数据，限制为当前用户的数据
	stats, err := getModuleStatsForUser(c.Request.Context(), statService, period, startDate, endDate, currentUser.ID, appID, "")
	if err != nil {
		logger.Error("获取服务使用数据失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取服务使用数据失败"})
		return
	}

	// 构建服务分布数据
	response := ServiceUsageResponse{
		Labels: []string{"LLM", "TTS", "ASR"},
		Datasets: []ServiceData{
			{
				Data: []float64{
					stats.LLM.Cost,
					stats.TTS.Cost,
					stats.ASR.Cost,
				},
				BackgroundColor: []string{
					"rgba(59, 130, 246, 0.8)",
					"rgba(16, 185, 129, 0.8)",
					"rgba(139, 92, 246, 0.8)",
				},
				BorderWidth: 0,
			},
		},
	}

	c.JSON(http.StatusOK, response)
}

// GetBillingPeriods 获取计费周期数据
func GetBillingPeriods(c *gin.Context) {
	// 获取当前用户
	currentUser := api.CurrentUser(c)
	if currentUser == nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未登录"})
		return
	}

	period := c.DefaultQuery("period", "30d")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")
	appID := cast.ToUint64(c.Query("app_id"))
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("pageSize", "20")

	// 如果指定了appID，验证该应用是否属于当前用户
	if appID != 0 {
		if !isUserApp(c.Request.Context(), currentUser.ID, appID) {
			c.JSON(http.StatusForbidden, gin.H{"error": "无权访问该应用数据"})
			return
		}
	}

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	db := cosy.UseDB(c.Request.Context())

	// 解析时间范围
	var startTime, endTime time.Time
	if startDate != "" && endDate != "" {
		startTime, err = time.Parse("2006-01-02", startDate)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "开始日期格式错误"})
			return
		}
		endTime, err = time.Parse("2006-01-02", endDate)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "结束日期格式错误"})
			return
		}
		endTime = endTime.Add(24*time.Hour - time.Second)
	} else {
		now := time.Now()
		switch period {
		case "today":
			startTime = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
			endTime = now
		case "7d":
			startTime = now.AddDate(0, 0, -7)
			endTime = now
		case "30d":
			startTime = now.AddDate(0, 0, -30)
			endTime = now
		case "90d":
			startTime = now.AddDate(0, 0, -90)
			endTime = now
		case "monthly":
			startTime = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
			endTime = now
		default:
			startTime = now.AddDate(0, 0, -30)
			endTime = now
		}
	}

	// 获取用户的应用ID列表
	var userAppIDs []uint64
	if appID != 0 {
		userAppIDs = []uint64{appID}
	} else {
		userAppIDs, err = getUserApps(c.Request.Context(), currentUser.ID)
		if err != nil {
			logger.Error("获取用户应用失败", "error", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "获取应用数据失败"})
			return
		}
	}

	// 如果用户没有应用，返回空结果
	if len(userAppIDs) == 0 {
		response := map[string]interface{}{
			"data":     []BillingPeriodResponse{},
			"total":    0,
			"page":     page,
			"pageSize": pageSize,
		}
		c.JSON(http.StatusOK, response)
		return
	}

	// 使用 query 包查询 Billing 表
	q := query.Use(db).Billing
	queryBuilder := q.Where(
		q.CreatedAt.Between(startTime.UnixMilli(), endTime.UnixMilli()),
		q.AppID.In(userAppIDs...),
	)

	// 获取总数
	total, err := queryBuilder.Count()
	if err != nil {
		logger.Error("获取计费记录总数失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取计费记录失败"})
		return
	}

	// 分页查询
	offset := (page - 1) * pageSize
	billings, err := queryBuilder.
		Order(q.CreatedAt.Desc()).
		Limit(pageSize).
		Offset(offset).
		Find()
	if err != nil {
		logger.Error("获取计费记录失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取计费记录失败"})
		return
	}

	// 转换为响应格式
	var records []BillingPeriodResponse
	for _, billing := range billings {
		costFloat, _ := billing.Cost.Float64()
		records = append(records, BillingPeriodResponse{
			ID:        billing.ID,
			Module:    billing.Module,
			Usage:     billing.Usage,
			Cost:      costFloat,
			Calls:     billing.Calls,
			CreatedAt: billing.CreatedAt,
		})
	}

	response := map[string]interface{}{
		"data": records,
		"pagination": map[string]interface{}{
			"total_pages":  int(math.Ceil(float64(total) / float64(pageSize))),
			"total":        total,
			"per_page":     pageSize,
			"current_page": page,
		},
	}

	c.JSON(http.StatusOK, response)
}

// GetApplicationBilling 获取应用的计费数据
func GetApplicationBilling(c *gin.Context) {
	// 获取当前用户
	currentUser := api.CurrentUser(c)
	if currentUser == nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未登录"})
		return
	}

	appID := cast.ToUint64(c.Param("app_id"))
	period := c.DefaultQuery("period", "30d")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	// 验证应用是否属于当前用户
	if !isUserApp(c.Request.Context(), currentUser.ID, appID) {
		c.JSON(http.StatusForbidden, gin.H{"error": "无权访问该应用数据"})
		return
	}

	// 获取统计服务实例
	statService := billing.GetStatServiceInstance()
	if statService == nil {
		logger.Error("获取统计服务失败")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "统计服务不可用"})
		return
	}

	// 获取应用的统计数据
	stats, err := statService.GetModuleStatsWithParams(c.Request.Context(), period, startDate, endDate, appID, "")
	if err != nil {
		logger.Error("获取应用统计数据失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取应用统计数据失败"})
		return
	}

	// 获取趋势数据
	trends, err := statService.GetModuleTrendsWithParams(c.Request.Context(), period, "calls", startDate, endDate, appID)
	if err != nil {
		logger.Error("获取应用趋势数据失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取应用趋势数据失败"})
		return
	}

	// 构建响应
	totalCalls := stats.LLM.Calls + stats.TTS.Calls + stats.ASR.Calls
	totalCost := stats.LLM.Cost + stats.TTS.Cost + stats.ASR.Cost

	var avgCostPerCall float64
	if totalCalls > 0 {
		avgCostPerCall = totalCost / float64(totalCalls)
	}

	response := ApplicationBillingResponse{
		Stats: BillingStatsResponse{
			TotalCalls: totalCalls,
			TotalUsage: ModuleUsage{
				LLM: stats.LLM.Usage,
				TTS: stats.TTS.Usage,
				ASR: stats.ASR.Usage,
			},
			TotalCost:      totalCost,
			AvgCostPerCall: avgCostPerCall,
		},
		Trends: []UsageTrendResponse{
			{
				Labels: trends.Labels,
				Datasets: []TrendData{
					{
						Label: "调用次数",
						Data:  aggregateDatasets(trends.Datasets),
					},
				},
			},
		},
		Services: []ServiceUsageResponse{
			{
				Labels: []string{"LLM", "TTS", "ASR"},
				Datasets: []ServiceData{
					{
						Data: []float64{
							stats.LLM.Cost,
							stats.TTS.Cost,
							stats.ASR.Cost,
						},
						BackgroundColor: []string{
							"rgba(59, 130, 246, 0.8)",
							"rgba(16, 185, 129, 0.8)",
							"rgba(139, 92, 246, 0.8)",
						},
						BorderWidth: 0,
					},
				},
			},
		},
	}

	c.JSON(http.StatusOK, response)
}

// ExportBillingReport 导出计费报告
func ExportBillingReport(c *gin.Context) {
	// 获取当前用户
	currentUser := api.CurrentUser(c)
	if currentUser == nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未登录"})
		return
	}

	period := c.DefaultQuery("period", "30d")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")
	appID := cast.ToUint64(c.Query("app_id"))

	// 如果指定了appID，验证该应用是否属于当前用户
	if appID != 0 {
		if !isUserApp(c.Request.Context(), currentUser.ID, appID) {
			c.JSON(http.StatusForbidden, gin.H{"error": "无权访问该应用数据"})
			return
		}
	}

	// 获取统计服务实例
	statService := billing.GetStatServiceInstance()
	if statService == nil {
		logger.Error("获取统计服务失败")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "统计服务不可用"})
		return
	}

	// 获取统计数据，限制为当前用户
	stats, err := getModuleStatsForUser(c.Request.Context(), statService, period, startDate, endDate, currentUser.ID, appID, "")
	if err != nil {
		logger.Error("获取统计数据失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取统计数据失败"})
		return
	}

	// 生成CSV内容
	csvContent := "模块,调用次数,使用量,总费用,平均单价\n"
	csvContent += fmt.Sprintf("LLM,%d,%d,%.2f,%.4f\n", stats.LLM.Calls, stats.LLM.Usage, stats.LLM.Cost, stats.LLM.AvgCostPerCall)
	csvContent += fmt.Sprintf("TTS,%d,%d,%.2f,%.4f\n", stats.TTS.Calls, stats.TTS.Usage, stats.TTS.Cost, stats.TTS.AvgCostPerCall)
	csvContent += fmt.Sprintf("ASR,%d,%d,%.2f,%.4f\n", stats.ASR.Calls, stats.ASR.Usage, stats.ASR.Cost, stats.ASR.AvgCostPerCall)

	// 设置响应头
	c.Header("Content-Type", "text/csv")
	c.Header("Content-Disposition", "attachment; filename=billing_report.csv")

	c.String(http.StatusOK, csvContent)
}

// 辅助函数

// getMetricLabel 获取指标标签
func getMetricLabel(metric string) string {
	switch metric {
	case "calls":
		return "调用次数"
	case "usage":
		return "使用量"
	case "cost":
		return "费用"
	default:
		return "调用次数"
	}
}

// aggregateDatasets 聚合数据集
func aggregateDatasets(datasets []billing.ModuleTrendWithParamsDataset) []float64 {
	if len(datasets) == 0 {
		return []float64{}
	}

	// 如果只有一个数据集，直接返回
	if len(datasets) == 1 {
		return datasets[0].Data
	}

	// 聚合多个数据集
	maxLen := 0
	for _, dataset := range datasets {
		if len(dataset.Data) > maxLen {
			maxLen = len(dataset.Data)
		}
	}

	result := make([]float64, maxLen)
	for i := 0; i < maxLen; i++ {
		sum := 0.0
		for _, dataset := range datasets {
			if i < len(dataset.Data) {
				sum += dataset.Data[i]
			}
		}
		result[i] = sum
	}

	return result
}

// isUserApp 检查应用是否属于指定用户
func isUserApp(ctx context.Context, userID, appID uint64) bool {
	db := cosy.UseDB(ctx)
	q := query.Use(db).App

	count, err := q.Where(q.ID.Eq(appID), q.UserID.Eq(userID)).Count()
	if err != nil {
		logger.Error("检查应用归属失败", "error", err)
		return false
	}

	return count > 0
}

// getUserApps 获取用户的所有应用ID
func getUserApps(ctx context.Context, userID uint64) ([]uint64, error) {
	db := cosy.UseDB(ctx)
	q := query.Use(db).App

	apps, err := q.Where(q.UserID.Eq(userID)).Find()
	if err != nil {
		return nil, err
	}

	var appIDs []uint64
	for _, app := range apps {
		appIDs = append(appIDs, app.ID)
	}

	return appIDs, nil
}

// getModuleStatsForUser 获取用户的模块统计数据
func getModuleStatsForUser(ctx context.Context, statService *billing.StatService, period, startDate, endDate string, userID, appID uint64, module string) (*billing.ModuleStatsWithParamsResponse, error) {
	// 如果没有指定appID，获取用户所有应用的统计
	if appID == 0 {
		userApps, err := getUserApps(ctx, userID)
		if err != nil {
			return nil, fmt.Errorf("获取用户应用失败: %w", err)
		}

		// 如果用户没有应用，返回空统计
		if len(userApps) == 0 {
			return &billing.ModuleStatsWithParamsResponse{
				LLM: billing.ModuleStatWithParamsItem{},
				TTS: billing.ModuleStatWithParamsItem{},
				ASR: billing.ModuleStatWithParamsItem{},
			}, nil
		}

		// 聚合所有应用的统计数据
		var totalStats billing.ModuleStatsWithParamsResponse
		for _, appID := range userApps {
			stats, err := statService.GetModuleStatsWithParams(ctx, period, startDate, endDate, appID, module)
			if err != nil {
				continue // 忽略单个应用的错误
			}

			// 聚合数据
			totalStats.LLM.Calls += stats.LLM.Calls
			totalStats.LLM.Usage += stats.LLM.Usage
			totalStats.LLM.Cost += stats.LLM.Cost

			totalStats.TTS.Calls += stats.TTS.Calls
			totalStats.TTS.Usage += stats.TTS.Usage
			totalStats.TTS.Cost += stats.TTS.Cost

			totalStats.ASR.Calls += stats.ASR.Calls
			totalStats.ASR.Usage += stats.ASR.Usage
			totalStats.ASR.Cost += stats.ASR.Cost
		}

		// 计算平均值
		if totalStats.LLM.Calls > 0 {
			totalStats.LLM.AvgCostPerCall = totalStats.LLM.Cost / float64(totalStats.LLM.Calls)
		}
		if totalStats.TTS.Calls > 0 {
			totalStats.TTS.AvgCostPerCall = totalStats.TTS.Cost / float64(totalStats.TTS.Calls)
		}
		if totalStats.ASR.Calls > 0 {
			totalStats.ASR.AvgCostPerCall = totalStats.ASR.Cost / float64(totalStats.ASR.Calls)
		}

		return &totalStats, nil
	}

	// 指定了appID，直接获取该应用的统计
	return statService.GetModuleStatsWithParams(ctx, period, startDate, endDate, appID, module)
}

// getModuleTrendsForUser 获取用户的模块趋势数据
func getModuleTrendsForUser(ctx context.Context, statService *billing.StatService, period, metric, startDate, endDate string, userID, appID uint64) (*billing.ModuleTrendsWithParamsResponse, error) {
	// 如果没有指定appID，获取用户所有应用的趋势
	if appID == 0 {
		userApps, err := getUserApps(ctx, userID)
		if err != nil {
			return nil, fmt.Errorf("获取用户应用失败: %w", err)
		}

		// 如果用户没有应用，返回空趋势
		if len(userApps) == 0 {
			return &billing.ModuleTrendsWithParamsResponse{
				Labels:   []string{},
				Datasets: []billing.ModuleTrendWithParamsDataset{},
			}, nil
		}

		// 获取第一个应用的趋势作为基础
		baseTrends, err := statService.GetModuleTrendsWithParams(ctx, period, metric, startDate, endDate, userApps[0])
		if err != nil {
			return nil, err
		}

		// 如果只有一个应用，直接返回
		if len(userApps) == 1 {
			return baseTrends, nil
		}

		// 聚合其他应用的数据
		for i := 1; i < len(userApps); i++ {
			trends, err := statService.GetModuleTrendsWithParams(ctx, period, metric, startDate, endDate, userApps[i])
			if err != nil {
				continue // 忽略单个应用的错误
			}

			// 聚合数据集
			for j, dataset := range trends.Datasets {
				if j < len(baseTrends.Datasets) {
					for k, value := range dataset.Data {
						if k < len(baseTrends.Datasets[j].Data) {
							baseTrends.Datasets[j].Data[k] += value
						}
					}
				}
			}
		}

		return baseTrends, nil
	}

	// 指定了appID，直接获取该应用的趋势
	return statService.GetModuleTrendsWithParams(ctx, period, metric, startDate, endDate, appID)
}

// getDashboardStatsForUser 获取用户的仪表板统计数据
func getDashboardStatsForUser(ctx context.Context, statService *billing.StatService, period string, userAppIDs []uint64) (map[string]interface{}, error) {
	db := cosy.UseDB(ctx)

	// 获取当前时间
	now := time.Now()
	var startTime time.Time

	switch period {
	case "today":
		startTime = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	case "7d":
		startTime = now.AddDate(0, 0, -7)
	case "30d":
		startTime = now.AddDate(0, 0, -30)
	case "90d":
		startTime = now.AddDate(0, 0, -90)
	case "monthly":
		startTime = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	default:
		startTime = now.AddDate(0, 0, -30)
	}

	startTimeMs := startTime.UnixMilli()
	endTimeMs := now.UnixMilli()

	// 初始化统计数据
	var totalCost float64
	var totalUsage int64
	var requestCount int64

	// 查询用户应用的使用记录
	q := query.Use(db).UsageLog
	usageLogs, err := q.Where(
		q.AppID.In(userAppIDs...),
		q.CreatedAt.Between(startTimeMs, endTimeMs),
	).Find()

	if err != nil {
		return nil, fmt.Errorf("查询使用记录失败: %w", err)
	}

	// 聚合统计数据
	for _, log := range usageLogs {
		totalCost += log.Cost
		totalUsage += log.Usage
		requestCount++
	}

	// 获取应用数量
	appCount := len(userAppIDs)

	response := map[string]interface{}{
		"total_cost":    totalCost,
		"total_usage":   totalUsage,
		"app_count":     appCount,
		"request_count": requestCount,
	}

	return response, nil
}

// calculateTodayCostAndGrowth 计算今日费用和增长率
func calculateTodayCostAndGrowth(ctx context.Context, statService *billing.StatService, userID, appID uint64) (float64, float64, error) {
	db := cosy.UseDB(ctx)
	now := time.Now()

	// 今日开始时间
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	todayStartMs := todayStart.UnixMilli()
	nowMs := now.UnixMilli()

	// 昨日开始和结束时间
	yesterdayStart := todayStart.AddDate(0, 0, -1)
	yesterdayStartMs := yesterdayStart.UnixMilli()
	yesterdayEndMs := todayStart.UnixMilli() - 1

	// 获取用户应用ID列表
	var userAppIDs []uint64
	var err error
	if appID != 0 {
		userAppIDs = []uint64{appID}
	} else {
		userAppIDs, err = getUserApps(ctx, userID)
		if err != nil {
			return 0, 0, fmt.Errorf("获取用户应用失败: %w", err)
		}
	}

	if len(userAppIDs) == 0 {
		return 0, 0, nil
	}

	// 计算今日费用
	var todayCost float64
	err = db.Model(&model.UsageLog{}).
		Where("app_id IN ? AND created_at BETWEEN ? AND ?", userAppIDs, todayStartMs, nowMs).
		Select("COALESCE(SUM(cost), 0)").
		Scan(&todayCost).Error
	if err != nil {
		return 0, 0, fmt.Errorf("计算今日费用失败: %w", err)
	}

	// 计算昨日费用
	var yesterdayCost float64
	err = db.Model(&model.UsageLog{}).
		Where("app_id IN ? AND created_at BETWEEN ? AND ?", userAppIDs, yesterdayStartMs, yesterdayEndMs).
		Select("COALESCE(SUM(cost), 0)").
		Scan(&yesterdayCost).Error
	if err != nil {
		return 0, 0, fmt.Errorf("计算昨日费用失败: %w", err)
	}

	// 计算增长率
	var growth float64
	if yesterdayCost > 0 {
		growth = ((todayCost - yesterdayCost) / yesterdayCost) * 100
	} else if todayCost > 0 {
		growth = 100 // 如果昨日费用为0，今日有费用，则增长100%
	}

	return todayCost, growth, nil
}

// calculateMonthlyCostAndGrowth 计算月度费用和增长率
func calculateMonthlyCostAndGrowth(ctx context.Context, statService *billing.StatService, userID, appID uint64) (float64, float64, error) {
	db := cosy.UseDB(ctx)
	now := time.Now()

	// 本月开始时间
	thisMonthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	thisMonthStartMs := thisMonthStart.UnixMilli()
	nowMs := now.UnixMilli()

	// 上月开始和结束时间
	lastMonthStart := thisMonthStart.AddDate(0, -1, 0)
	lastMonthStartMs := lastMonthStart.UnixMilli()
	lastMonthEndMs := thisMonthStart.UnixMilli() - 1

	// 获取用户应用ID列表
	var userAppIDs []uint64
	var err error
	if appID != 0 {
		userAppIDs = []uint64{appID}
	} else {
		userAppIDs, err = getUserApps(ctx, userID)
		if err != nil {
			return 0, 0, fmt.Errorf("获取用户应用失败: %w", err)
		}
	}

	if len(userAppIDs) == 0 {
		return 0, 0, nil
	}

	// 计算本月费用
	var thisMonthCost float64
	err = db.Model(&model.UsageLog{}).
		Where("app_id IN ? AND created_at BETWEEN ? AND ?", userAppIDs, thisMonthStartMs, nowMs).
		Select("COALESCE(SUM(cost), 0)").
		Scan(&thisMonthCost).Error
	if err != nil {
		return 0, 0, fmt.Errorf("计算本月费用失败: %w", err)
	}

	// 计算上月费用
	var lastMonthCost float64
	err = db.Model(&model.UsageLog{}).
		Where("app_id IN ? AND created_at BETWEEN ? AND ?", userAppIDs, lastMonthStartMs, lastMonthEndMs).
		Select("COALESCE(SUM(cost), 0)").
		Scan(&lastMonthCost).Error
	if err != nil {
		return 0, 0, fmt.Errorf("计算上月费用失败: %w", err)
	}

	// 计算增长率
	var growth float64
	if lastMonthCost > 0 {
		growth = ((thisMonthCost - lastMonthCost) / lastMonthCost) * 100
	} else if thisMonthCost > 0 {
		growth = 100 // 如果上月费用为0，本月有费用，则增长100%
	}

	return thisMonthCost, growth, nil
}

// calculateDailyAverageCost 计算日均费用
func calculateDailyAverageCost(ctx context.Context, statService *billing.StatService, period, startDate, endDate string, userID, appID uint64) (float64, error) {
	db := cosy.UseDB(ctx)

	// 解析时间范围
	var startTime, endTime time.Time
	var err error

	if startDate != "" && endDate != "" {
		startTime, err = time.Parse("2006-01-02", startDate)
		if err != nil {
			return 0, fmt.Errorf("解析开始日期失败: %w", err)
		}
		endTime, err = time.Parse("2006-01-02", endDate)
		if err != nil {
			return 0, fmt.Errorf("解析结束日期失败: %w", err)
		}
		endTime = endTime.Add(24*time.Hour - time.Second)
	} else {
		now := time.Now()
		switch period {
		case "today":
			startTime = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
			endTime = now
		case "7d":
			startTime = now.AddDate(0, 0, -7)
			endTime = now
		case "30d":
			startTime = now.AddDate(0, 0, -30)
			endTime = now
		case "90d":
			startTime = now.AddDate(0, 0, -90)
			endTime = now
		case "monthly":
			startTime = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
			endTime = now
		default:
			startTime = now.AddDate(0, 0, -30)
			endTime = now
		}
	}

	startTimeMs := startTime.UnixMilli()
	endTimeMs := endTime.UnixMilli()

	// 获取用户应用ID列表
	var userAppIDs []uint64
	if appID != 0 {
		userAppIDs = []uint64{appID}
	} else {
		userAppIDs, err = getUserApps(ctx, userID)
		if err != nil {
			return 0, fmt.Errorf("获取用户应用失败: %w", err)
		}
	}

	if len(userAppIDs) == 0 {
		return 0, nil
	}

	// 计算总费用
	var totalCost float64
	err = db.Model(&model.UsageLog{}).
		Where("app_id IN ? AND created_at BETWEEN ? AND ?", userAppIDs, startTimeMs, endTimeMs).
		Select("COALESCE(SUM(cost), 0)").
		Scan(&totalCost).Error
	if err != nil {
		return 0, fmt.Errorf("计算总费用失败: %w", err)
	}

	// 计算天数
	days := endTime.Sub(startTime).Hours() / 24
	if days <= 0 {
		days = 1 // 至少1天
	}

	// 计算日均费用
	dailyAverage := totalCost / days

	return dailyAverage, nil
}

// calculateMonthlyCalls 计算月度请求次数
func calculateMonthlyCalls(ctx context.Context, statService *billing.StatService, userID, appID uint64) (int64, error) {
	db := cosy.UseDB(ctx)
	now := time.Now()

	// 本月开始时间
	thisMonthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	thisMonthStartMs := thisMonthStart.UnixMilli()
	nowMs := now.UnixMilli()

	// 获取用户应用ID列表
	var userAppIDs []uint64
	var err error
	if appID != 0 {
		userAppIDs = []uint64{appID}
	} else {
		userAppIDs, err = getUserApps(ctx, userID)
		if err != nil {
			return 0, fmt.Errorf("获取用户应用失败: %w", err)
		}
	}

	if len(userAppIDs) == 0 {
		return 0, nil
	}

	// 计算本月请求次数
	var monthlyCalls int64
	err = db.Model(&model.UsageLog{}).
		Where("app_id IN ? AND created_at BETWEEN ? AND ?", userAppIDs, thisMonthStartMs, nowMs).
		Count(&monthlyCalls).Error
	if err != nil {
		return 0, fmt.Errorf("计算月度请求次数失败: %w", err)
	}

	return monthlyCalls, nil
}
