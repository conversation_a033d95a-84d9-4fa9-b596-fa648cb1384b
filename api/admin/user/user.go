package user

import (
	"crypto/rand"
	"fmt"
	"math/big"
	"net/http"

	"git.uozi.org/uozi/potato-billing-api/internal/user"
	"git.uozi.org/uozi/potato-billing-api/model"
	"git.uozi.org/uozi/potato-billing-api/query"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// UpdateAvatarRequest 头像更新请求
type UpdateAvatarRequest struct {
	AvatarID string `json:"avatar_id" binding:"required"`
}

// UpdateStatusRequest 状态更新请求
type UpdateStatusRequest struct {
	Status int `json:"status" binding:"required,oneof=0 1"`
}

// UserStatsResponse 用户统计响应
type UserStatsResponse struct {
	AppCount      int     `json:"app_count"`
	TotalUsage    int64   `json:"total_usage"`
	TotalCost     float64 `json:"total_cost"`
	RechargeCount int     `json:"recharge_count"`
	TotalRecharge float64 `json:"total_recharge"`
}

// GeneratePasswordResponse 生成密码响应
type GeneratePasswordResponse struct {
	Password string `json:"password"`
}

func InitUserRouter(g *gin.RouterGroup) {
	c := cosy.Api[model.User]("users")

	c.CreateHook(func(c *cosy.Ctx[model.User]) {
		c.BeforeDecodeHook(user.EncryptPassword)
	})

	c.ModifyHook(func(c *cosy.Ctx[model.User]) {
		c.BeforeDecodeHook(user.EncryptPassword)
		c.AddSelectedFields("phone_like")
	})

	c.BeforeGetList(func(c *gin.Context) {
		user.PersistLastActive()
	})

	c.GetListHook(func(c *cosy.Ctx[model.User]) {

	})

	c.DestroyHook(func(c *cosy.Ctx[model.User]) {
		c.BeforeExecuteHook(func(ctx *cosy.Ctx[model.User]) {
			if ctx.OriginModel.ID == 1 {
				ctx.JSON(http.StatusNotAcceptable, gin.H{
					"message": "Cannot delete the super admin",
				})

				ctx.Abort()
			}
		})

	})

	c.InitRouter(g)

	// 扩展API路由
	g.GET("/users/search", SearchUsers)
	g.POST("/users/:id/generate-password", GeneratePassword)
	g.PATCH("/users/:id/avatar", UpdateAvatar)
	g.PATCH("/users/:id/status", UpdateStatus)
	g.GET("/users/:id/stats", GetUserStats)
}

// GeneratePassword 生成随机密码并重置
func GeneratePassword(c *gin.Context) {
	userID := cast.ToUint64(c.Param("id"))
	if userID == 0 {
		cosy.ErrHandler(c, fmt.Errorf("invalid user id"))
		return
	}

	// 生成随机密码
	password := generateRandomPassword(12)

	// 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 更新密码
	u := query.User
	_, err = u.Where(u.ID.Eq(userID)).Update(u.Password, string(hashedPassword))
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, GeneratePasswordResponse{
		Password: password,
	})
}

// UpdateAvatar 更新用户头像
func UpdateAvatar(c *gin.Context) {
	userID := cast.ToUint64(c.Param("id"))
	if userID == 0 {
		cosy.ErrHandler(c, fmt.Errorf("invalid user id"))
		return
	}

	var req UpdateAvatarRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	avatarID := cast.ToUint64(req.AvatarID)
	if avatarID == 0 {
		cosy.ErrHandler(c, fmt.Errorf("invalid avatar id"))
		return
	}

	// 更新头像
	u := query.User
	_, err := u.Where(u.ID.Eq(userID)).Update(u.AvatarID, avatarID)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 获取更新后的用户信息
	user, err := u.Preload(u.Avatar).Where(u.ID.Eq(userID)).First()
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 隐藏密码
	user.Password = ""

	c.JSON(http.StatusOK, user)
}

// UpdateStatus 更新用户状态
func UpdateStatus(c *gin.Context) {
	userID := cast.ToUint64(c.Param("id"))
	if userID == 0 {
		cosy.ErrHandler(c, fmt.Errorf("invalid user id"))
		return
	}

	var req UpdateStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 更新状态
	u := query.User
	_, err := u.Where(u.ID.Eq(userID)).Update(u.Status, req.Status)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 获取更新后的用户信息
	user, err := u.Preload(u.Avatar).Where(u.ID.Eq(userID)).First()
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 隐藏密码
	user.Password = ""

	c.JSON(http.StatusOK, user)
}

// GetUserStats 获取用户统计信息
func GetUserStats(c *gin.Context) {
	userID := cast.ToUint64(c.Param("id"))
	if userID == 0 {
		cosy.ErrHandler(c, fmt.Errorf("invalid user id"))
		return
	}

	var stats UserStatsResponse
	db := cosy.UseDB(c)

	// 获取API Key数量
	var apiKeyCount int64
	err := db.Model(&model.App{}).Where("user_id = ?", userID).Count(&apiKeyCount).Error
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}
	stats.AppCount = int(apiKeyCount)

	// 获取充值统计
	var rechargeCount int64
	var totalRecharge float64
	err = db.Model(&model.RechargeRecord{}).
		Where("user_id = ?", userID).
		Count(&rechargeCount).Error
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}
	stats.RechargeCount = int(rechargeCount)

	err = db.Model(&model.RechargeRecord{}).
		Where("user_id = ? AND status = ?", userID, "completed").
		Select("COALESCE(SUM(amount), 0)").
		Scan(&totalRecharge).Error
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}
	stats.TotalRecharge = totalRecharge

	var usageStats struct {
		TotalUsage int64   `json:"total_usage"`
		TotalCost  float64 `json:"total_cost"`
	}

	// 首先获取用户的所有API Key
	apps := query.App
	userApps, err := apps.Where(apps.UserID.Eq(userID)).Find()
	if err != nil && err != gorm.ErrRecordNotFound {
		cosy.ErrHandler(c, err)
		return
	}

	if len(userApps) > 0 {
		// 构建AppID列表
		var appIDList []uint64
		for _, app := range userApps {
			appIDList = append(appIDList, app.ID)
		}

		// 统计总使用量和成本
		err = db.Model(&model.UsageLog{}).
			Where("app_id IN ?", appIDList).
			Select("COALESCE(SUM(`usage`), 0) as total_usage, COALESCE(SUM(`cost`), 0) as total_cost").
			Scan(&usageStats).Error
		if err != nil {
			cosy.ErrHandler(c, err)
			return
		}

		stats.TotalUsage = usageStats.TotalUsage
		stats.TotalCost = usageStats.TotalCost
	}

	c.JSON(http.StatusOK, stats)
}

// SearchUsersResponse 用户搜索结果
type SearchUsersResponse struct {
	ID     uint64 `json:"id,string"`
	Name   string `json:"name"`
	Email  string `json:"email"`
	Avatar string `json:"avatar,omitempty"`
}

// SearchUsers 搜索用户
func SearchUsers(c *gin.Context) {
	searchQuery := c.Query("q")
	if searchQuery == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "搜索关键词不能为空"})
		return
	}

	// 获取分页参数
	limit := 20
	if limitParam := c.Query("limit"); limitParam != "" {
		if l := cast.ToInt(limitParam); l > 0 && l <= 100 {
			limit = l
		}
	}

	q := query.User

	// 构建搜索条件：用户名或邮箱包含关键词，预加载头像信息
	users, err := q.Where(
		q.Name.Like("%" + searchQuery + "%"),
	).Or(q.Email.Like("%" + searchQuery + "%")).
		Preload(q.Avatar).Limit(limit).Find()

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "搜索用户失败"})
		return
	}

	// 转换为响应格式
	result := make([]SearchUsersResponse, len(users))
	for i, user := range users {
		avatarURL := ""
		if user.Avatar != nil {
			avatarURL = user.Avatar.Path
		}

		result[i] = SearchUsersResponse{
			ID:     user.ID,
			Name:   user.Name,
			Email:  user.Email,
			Avatar: avatarURL,
		}
	}

	c.JSON(http.StatusOK, result)
}

// generateRandomPassword 生成随机密码
func generateRandomPassword(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*"
	password := make([]byte, length)

	for i := range password {
		n, _ := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
		password[i] = charset[n.Int64()]
	}

	return string(password)
}
