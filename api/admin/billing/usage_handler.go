package billing

import (
	"errors"
	"net/http"

	"git.uozi.org/uozi/potato-billing-api/internal/billing"
	"git.uozi.org/uozi/potato-billing-api/model"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
	"gorm.io/gorm"
)

// UsageHistoryResponse 用量历史响应结构
type UsageHistoryResponse struct {
	Total int64            `json:"total"`
	Items []model.UsageLog `json:"items"`
}

// RegisterUsageAPI 初始化用量API
func RegisterUsageAPI(g *gin.RouterGroup) {
	g.GET("/apps/:app_id/usage", GetAppUsageHistory)
	g.GET("/apps/:app_id/stats", GetAppUsageStats)
}

// GetAppUsageHistory 获取用量历史
func GetAppUsageHistory(c *gin.Context) {
	appID := c.Param("app_id")
	if appID == "" {
		cosy.ErrHandler(c, errors.New("应用ID参数必需"))
		return
	}

	cosy.Core[model.UsageLog](c).SetValidRules(gin.H{
		"module":     "in",
		"start_time": "gte",
		"end_time":   "lte",
	}).GormScope(func(tx *gorm.DB) *gorm.DB {
		return tx.Where("app_id = ?", appID)
	}).PagingList()
}

// GetAppUsageStats 获取用量统计
func GetAppUsageStats(c *gin.Context) {
	appID := c.Param("app_id")
	if appID == "" {
		cosy.ErrHandler(c, errors.New("应用ID参数必需"))
		return
	}

	period := c.DefaultQuery("period", "month")

	// 获取统计服务实例
	statService := billing.GetStatServiceInstance()
	if statService == nil {
		cosy.ErrHandler(c, errors.New("统计服务不可用"))
		return
	}

	// 调用统计服务获取数据
	stats, err := statService.GetUsageStats(c.Request.Context(), appID, period)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, stats)
}
