package billing

import (
	"net/http"
	"strconv"
	"time"

	"git.uozi.org/uozi/potato-billing-api/api"
	"git.uozi.org/uozi/potato-billing-api/internal/billing"
	"git.uozi.org/uozi/potato-billing-api/model"
	"git.uozi.org/uozi/potato-billing-api/query"
	"git.uozi.org/uozi/potato-billing-api/types"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/logger"
	"gorm.io/gorm"
)

// CreateRechargeRequest 创建充值记录请求
type CreateRechargeRequest struct {
	UserID      uint64  `json:"user_id,string" binding:"required"`
	Amount      float64 `json:"amount" binding:"required,min=0.01"`
	Type        string  `json:"type,omitempty"`
	Description string  `json:"description,omitempty"`
}

// RechargeStatsResponse 充值统计响应
type RechargeStatsResponse struct {
	TodayAmount     float64 `json:"today_amount"`     // 今日充值金额
	MonthAmount     float64 `json:"month_amount"`     // 本月充值金额
	TotalCount      int64   `json:"total_count"`      // 总充值次数
	AverageAmount   float64 `json:"average_amount"`   // 平均充值金额
	PendingAmount   float64 `json:"pending_amount"`   // 待处理金额
	CompletedAmount float64 `json:"completed_amount"` // 已完成金额
	FailedAmount    float64 `json:"failed_amount"`    // 失败金额
	TotalUsers      int64   `json:"total_users"`      // 总用户数
	ActiveUsers     int64   `json:"active_users"`     // 活跃用户数
	TotalBalance    float64 `json:"total_balance"`    // 用户总余额
}

// UserBalanceInfo 用户余额信息
type UserBalanceInfo struct {
	ID           string  `json:"id"`
	Name         string  `json:"name"`
	Email        string  `json:"email"`
	Balance      float64 `json:"balance"`
	LastRecharge string  `json:"last_recharge"`
}

// RegisterRechargeAPI 初始化充值API
func RegisterRechargeAPI(g *gin.RouterGroup) {
	// 自定义路由
	g.GET("/recharge_records", GetRechargeList)
	g.POST("/recharge_records", CreateRecharge)
	g.POST("/recharge_records/:id/confirm", ConfirmRecharge)
	g.POST("/recharge_records/:id/cancel", CancelRecharge)
	g.GET("/recharge_records/stats", GetRechargeStats)
	g.GET("/recharge_records/user-balances", GetUserBalances)
}

// GetRechargeList 获取充值记录列表
func GetRechargeList(c *gin.Context) {
	cosy.
		Core[model.RechargeRecord](c).
		SetEqual("user_id", "type").
		SetPreloads("User").
		PagingList()
}

// CreateRecharge 创建充值记录（管理员充值）
func CreateRecharge(c *gin.Context) {
	var req CreateRechargeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 获取操作员信息
	operator := api.CurrentUser(c)

	// 检查用户是否存在
	user, err := query.User.Where(query.User.ID.Eq(req.UserID)).First()
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 开启事务
	err = cosy.UseDB(c).Transaction(func(tx *gorm.DB) error {
		// 创建充值记录
		rechargeRecord := &model.RechargeRecord{
			UserID:      req.UserID,
			Amount:      req.Amount,
			Type:        req.Type,
			Status:      types.RechargeStatusCompleted, // 管理员充值直接完成
			Description: req.Description,
			OperatorID:  operator.ID,
		}

		if err := tx.Create(rechargeRecord).Error; err != nil {
			return err
		}

		// 增加用户余额
		err := tx.Model(user).
			Update("balance", gorm.Expr("balance + ?", req.Amount)).
			Error
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		logger.Error("Failed to create recharge record", "error", err)
		cosy.ErrHandler(c, err)
		return
	}

	// 充值成功后，检查并恢复被阻塞的Key状态
	billingService := billing.GetBillingService()
	if billingService != nil {
		keyService := billingService.GetAppService()
		if keyService != nil {
			err := keyService.CheckAndRestoreAppStatus(c.Request.Context(), req.UserID, 0, "")
			if err != nil {
				logger.Error("Failed to restore key status after recharge", "error", err, "userID", req.UserID)
				// 不影响充值成功的响应，只记录错误
			} else {
				logger.Info("Successfully checked and restored key status after recharge", "userID", req.UserID)
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "充值成功",
		"amount":  req.Amount,
	})
}

// ConfirmRecharge 确认充值（用于处理第三方支付回调）
func ConfirmRecharge(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 查询充值记录
	rechargeRecord, err := query.RechargeRecord.
		Preload(query.RechargeRecord.User).
		Where(query.RechargeRecord.ID.Eq(id)).
		First()
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	if rechargeRecord.Status != types.RechargeStatusPending {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "充值记录状态不允许确认",
		})
		return
	}

	// 开启事务
	err = cosy.UseDB(c).Transaction(func(tx *gorm.DB) error {
		// 更新充值记录状态
		err := tx.Model(rechargeRecord).Update("status", types.RechargeStatusCompleted).Error
		if err != nil {
			return err
		}

		// 增加用户余额
		err = tx.Model(rechargeRecord.User).Update("balance", gorm.Expr("balance + ?", rechargeRecord.Amount)).Error
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		logger.Error("Failed to confirm recharge", "error", err)
		cosy.ErrHandler(c, err)
		return
	}

	// 充值确认成功后，检查并恢复被阻塞的Key状态
	billingService := billing.GetBillingService()
	if billingService != nil {
		keyService := billingService.GetAppService()
		if keyService != nil {
			err := keyService.CheckAndRestoreAppStatus(c.Request.Context(), rechargeRecord.UserID, 0, "")
			if err != nil {
				logger.Error("Failed to restore key status after recharge confirmation", "error", err, "userID", rechargeRecord.UserID)
				// 不影响充值确认成功的响应，只记录错误
			} else {
				logger.Info("Successfully checked and restored key status after recharge confirmation", "userID", rechargeRecord.UserID)
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "充值确认成功",
	})
}

// CancelRecharge 取消充值
func CancelRecharge(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 查询充值记录
	rechargeRecord, err := query.RechargeRecord.
		Where(query.RechargeRecord.ID.Eq(id)).
		First()
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	if rechargeRecord.Status != types.RechargeStatusPending {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "只能取消待处理的充值记录",
		})
		return
	}

	// 更新状态
	_, err = query.RechargeRecord.
		Where(query.RechargeRecord.ID.Eq(id)).
		Update(query.RechargeRecord.Status, types.RechargeStatusCancelled)
	if err != nil {
		logger.Error("Failed to cancel recharge", "error", err)
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "充值已取消",
	})
}

// GetRechargeStats 获取充值统计数据
func GetRechargeStats(c *gin.Context) {
	// 获取统计服务实例
	statService := billing.GetStatServiceInstance()
	if statService == nil {
		logger.Error("获取统计服务失败")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "统计服务不可用"})
		return
	}

	// 调用统计服务获取数据
	stats, err := statService.GetRechargeStats(c.Request.Context())
	if err != nil {
		logger.Error("获取充值统计数据失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取统计数据失败"})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// GetUserBalances 获取用户余额排行
func GetUserBalances(c *gin.Context) {
	db := cosy.UseDB(c)

	// 查询用户余额排行，按余额降序排列，取前20名
	var users []model.User
	err := db.Model(&model.User{}).
		Where("balance > 0").
		Order("balance DESC").
		Limit(20).
		Find(&users).Error

	if err != nil {
		logger.Error("Failed to get user balances", "error", err)
		cosy.ErrHandler(c, err)
		return
	}

	// 转换为响应格式
	var userBalances []UserBalanceInfo
	for _, user := range users {
		// 查询用户最后一次充值时间
		var lastRecharge model.RechargeRecord
		lastRechargeTime := ""

		err := db.Model(&model.RechargeRecord{}).
			Where("user_id = ? AND status = ?", user.ID, types.RechargeStatusCompleted).
			Order("created_at DESC").
			First(&lastRecharge).Error

		if err == nil {
			lastRechargeTime = time.UnixMilli(lastRecharge.CreatedAt).Format("2006-01-02")
		}

		userBalances = append(userBalances, UserBalanceInfo{
			ID:           strconv.FormatUint(user.ID, 10),
			Name:         user.Name,
			Email:        user.Email,
			Balance:      user.Balance,
			LastRecharge: lastRechargeTime,
		})
	}

	c.JSON(http.StatusOK, userBalances)
}
