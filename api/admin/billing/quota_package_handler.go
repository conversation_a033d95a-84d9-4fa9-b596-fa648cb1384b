package billing

import (
	"net/http"
	"strconv"
	"time"

	"git.uozi.org/uozi/potato-billing-api/api"
	"git.uozi.org/uozi/potato-billing-api/internal/billing"
	"git.uozi.org/uozi/potato-billing-api/model"
	"git.uozi.org/uozi/potato-billing-api/query"
	"git.uozi.org/uozi/potato-billing-api/types"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/logger"
)

// CreateQuotaPackageRequest 创建资源包请求
type CreateQuotaPackageRequest struct {
	UserID      uint64 `json:"user_id,string" binding:"required"`
	AppID       uint64 `json:"app_id,string"`
	Module      string `json:"module" binding:"required"`
	ModelName   string `json:"model_name,omitempty"`
	Quota       int64  `json:"quota" binding:"required,min=1"`
	ExpiresAt   int64  `json:"expires_at,omitempty"`
	Type        string `json:"type,omitempty"`
	Description string `json:"description,omitempty"`
}

// UpdateQuotaPackageRequest 更新资源包请求
type UpdateQuotaPackageRequest struct {
	Quota       int64  `json:"quota,omitempty" binding:"omitempty,min=1"`
	ExpiresAt   int64  `json:"expires_at,omitempty"`
	Status      string `json:"status,omitempty"`
	Description string `json:"description,omitempty"`
}

// QuotaPackageOverviewStats 资源包概览统计
type QuotaPackageOverviewStats struct {
	Total        int64                    `json:"total"`         // 总资源包数量
	Active       int64                    `json:"active"`        // 活跃资源包数量
	Expired      int64                    `json:"expired"`       // 过期资源包数量
	Exhausted    int64                    `json:"exhausted"`     // 用完资源包数量
	Disabled     int64                    `json:"disabled"`      // 禁用资源包数量
	ExpiringSoon int64                    `json:"expiring_soon"` // 即将过期数量（7天内）
	TotalCost    float64                  `json:"total_cost"`    // 总成本
	TotalUsage   int64                    `json:"total_usage"`   // 总使用量（仅用于计算成本）
	ModuleStats  []QuotaPackageModuleStat `json:"module_stats"`  // 按模块统计
}

// QuotaPackageModuleStat 资源包模块统计
type QuotaPackageModuleStat struct {
	Module     string  `json:"module"`      // 模块名称
	Name       string  `json:"name"`        // 模块显示名称
	Count      int64   `json:"count"`       // 资源包数量
	TotalQuota int64   `json:"total_quota"` // 总配额
	TotalUsed  int64   `json:"total_used"`  // 总使用量
	Available  int64   `json:"available"`   // 可用量
	UsageRate  float64 `json:"usage_rate"`  // 使用率
	Unit       string  `json:"unit"`        // 单位
	Cost       float64 `json:"cost"`        // 模块总成本
}

// RegisterQuotaPackageAPI 初始化资源包API
func RegisterQuotaPackageAPI(g *gin.RouterGroup) {
	g.GET("/quota_packages", GetQuotaPackageList)
	g.POST("/quota_packages", CreateQuotaPackage)
	g.POST("/quota_packages/:id", UpdateQuotaPackage)
	g.GET("/quota_packages/user/:user_id", GetUserQuotaPackages)
	g.PUT("/quota_packages/:id/enable", EnableQuotaPackage)
	g.PUT("/quota_packages/:id/disable", DisableQuotaPackage)
	g.GET("/quota_packages/overview/stats", GetQuotaPackageOverviewStats)
}

// GetQuotaPackageList 获取资源包列表
func GetQuotaPackageList(c *gin.Context) {
	cosy.Core[model.QuotaPackageRecord](c).
		SetPreloads("User", "Operator", "App").
		PagingList()
}

// CreateQuotaPackage 创建资源包
func CreateQuotaPackage(c *gin.Context) {
	var req CreateQuotaPackageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 获取操作员信息
	operatorID := api.CurrentUser(c).ID

	// 创建资源包记录
	quotaPackage := &model.QuotaPackageRecord{
		UserID:      req.UserID,
		AppID:       req.AppID,
		Module:      req.Module,
		ModelName:   req.ModelName,
		Quota:       req.Quota,
		Used:        0,
		ExpiresAt:   0,
		Status:      types.QuotaPackageStatusActive,
		Type:        req.Type,
		Description: req.Description,
		OperatorID:  operatorID,
	}

	if req.ExpiresAt != 0 {
		quotaPackage.ExpiresAt = req.ExpiresAt
	}

	if req.Type == "" {
		quotaPackage.Type = types.QuotaPackageSourceAdmin
	}

	err := query.QuotaPackageRecord.Create(quotaPackage)
	if err != nil {
		logger.Error("Failed to create quota package", "error", err)
		cosy.ErrHandler(c, err)
		return
	}

	// 资源包创建成功后，检查并恢复相关Key状态
	billingService := billing.GetBillingService()
	if billingService != nil {
		keyService := billingService.GetAppService()
		if keyService != nil {
			err := keyService.CheckAndRestoreAppStatus(c.Request.Context(), req.UserID, req.AppID, req.Module)
			if err != nil {
				logger.Error("Failed to restore key status after quota package creation", "error", err,
					"userID", req.UserID, "appID", req.AppID, "module", req.Module)
				// 不影响资源包创建成功的响应，只记录错误
			} else {
				logger.Info("Successfully checked and restored key status after quota package creation",
					"userID", req.UserID, "appID", req.AppID, "module", req.Module)
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "资源包创建成功",
		"data":    quotaPackage,
	})
}

// UpdateQuotaPackage 更新资源包
func UpdateQuotaPackage(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	var req UpdateQuotaPackageRequest
	if bindErr := c.ShouldBindJSON(&req); bindErr != nil {
		cosy.ErrHandler(c, bindErr)
		return
	}

	updates := make(map[string]interface{})

	if req.Quota != 0 {
		updates["quota"] = req.Quota
	}
	if req.ExpiresAt != 0 {
		updates["expires_at"] = req.ExpiresAt
	}
	if req.Status != "" {
		updates["status"] = req.Status
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}

	err = cosy.UseDB(c).Model(&model.QuotaPackageRecord{}).
		Where("id = ?", id).
		Updates(updates).Error
	if err != nil {
		logger.Error("Failed to update quota package", "error", err)
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "资源包更新成功",
	})
}

// DisableQuotaPackage 禁用资源包
func DisableQuotaPackage(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	err = cosy.UseDB(c).Model(&model.QuotaPackageRecord{}).
		Where("id = ?", id).
		Update("status", types.QuotaPackageStatusDisabled).Error
	if err != nil {
		logger.Error("Failed to disable quota package", "error", err)
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "资源包已禁用",
	})
}

// EnableQuotaPackage 启用资源包
func EnableQuotaPackage(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 检查资源包是否过期
	var quotaPackage model.QuotaPackageRecord
	err = cosy.UseDB(c).Where("id = ?", id).First(&quotaPackage).Error
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	status := types.QuotaPackageStatusActive
	if quotaPackage.ExpiresAt > 0 && quotaPackage.ExpiresAt < time.Now().UnixMilli() {
		status = types.QuotaPackageStatusExpired
	} else if quotaPackage.Used >= quotaPackage.Quota {
		status = types.QuotaPackageStatusExhausted
	}

	err = cosy.UseDB(c).Model(&model.QuotaPackageRecord{}).
		Where("id = ?", id).
		Update("status", status).Error
	if err != nil {
		logger.Error("Failed to enable quota package", "error", err)
		cosy.ErrHandler(c, err)
		return
	}

	// 如果资源包被成功启用，检查并恢复相关Key状态
	if status == types.QuotaPackageStatusActive {
		billingService := billing.GetBillingService()
		if billingService != nil {
			appService := billingService.GetAppService()
			if appService != nil {
				err := appService.CheckAndRestoreAppStatus(c.Request.Context(), quotaPackage.UserID, quotaPackage.AppID, quotaPackage.Module)
				if err != nil {
					logger.Error("Failed to restore key status after quota package enable", "error", err,
						"userID", quotaPackage.UserID, "appID", quotaPackage.AppID, "module", quotaPackage.Module)
					// 不影响启用成功的响应，只记录错误
				} else {
					logger.Info("Successfully checked and restored key status after quota package enable",
						"userID", quotaPackage.UserID, "appID", quotaPackage.AppID, "module", quotaPackage.Module)
				}
			}
		}
	}

	message := "资源包已启用"
	if status != "active" {
		message = "资源包状态已更新为" + status
	}

	c.JSON(http.StatusOK, gin.H{
		"message": message,
		"status":  status,
	})
}

// GetUserQuotaPackages 获取用户资源包列表
func GetUserQuotaPackages(c *gin.Context) {
	userID := cast.ToUint64(c.Param("user_id"))

	// 获取查询参数
	module := c.Query("module")
	status := c.Query("status")
	appID := cast.ToUint64(c.Query("app_id"))

	q := query.QuotaPackageRecord.Where(query.QuotaPackageRecord.UserID.Eq(userID))

	if module != "" {
		q = q.Where(query.QuotaPackageRecord.Module.Eq(module))
	}
	if status != "" {
		q = q.Where(query.QuotaPackageRecord.Status.Eq(status))
	}
	if appID != 0 {
		q = q.Where(query.QuotaPackageRecord.AppID.Eq(appID))
	}

	quotaPackages, err := q.
		Preload(query.QuotaPackageRecord.User).
		Preload(query.QuotaPackageRecord.Operator).
		Order(query.QuotaPackageRecord.CreatedAt.Desc()).
		Find()
	if err != nil {
		logger.Error("Failed to get user quota packages", "error", err)
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":  quotaPackages,
		"total": len(quotaPackages),
	})
}

// GetQuotaPackageOverviewStats 获取资源包概览统计
func GetQuotaPackageOverviewStats(c *gin.Context) {
	db := cosy.UseDB(c)

	// 1. 获取基础统计数据
	var total, active, expired, exhausted, disabled int64

	// 总资源包数量
	if err := db.Model(&model.QuotaPackageRecord{}).Count(&total).Error; err != nil {
		logger.Error("Failed to count total quota packages", "error", err)
		cosy.ErrHandler(c, err)
		return
	}

	// 活跃资源包数量
	if err := db.Model(&model.QuotaPackageRecord{}).Where("status = ?", types.QuotaPackageStatusActive).Count(&active).Error; err != nil {
		logger.Error("Failed to count active quota packages", "error", err)
		cosy.ErrHandler(c, err)
		return
	}

	// 过期资源包数量
	if err := db.Model(&model.QuotaPackageRecord{}).Where("status = ?", types.QuotaPackageStatusExpired).Count(&expired).Error; err != nil {
		logger.Error("Failed to count expired quota packages", "error", err)
		cosy.ErrHandler(c, err)
		return
	}

	// 用完资源包数量
	if err := db.Model(&model.QuotaPackageRecord{}).Where("status = ?", types.QuotaPackageStatusExhausted).Count(&exhausted).Error; err != nil {
		logger.Error("Failed to count exhausted quota packages", "error", err)
		cosy.ErrHandler(c, err)
		return
	}

	// 禁用资源包数量
	if err := db.Model(&model.QuotaPackageRecord{}).Where("status = ?", types.QuotaPackageStatusDisabled).Count(&disabled).Error; err != nil {
		logger.Error("Failed to count disabled quota packages", "error", err)
		cosy.ErrHandler(c, err)
		return
	}

	// 即将过期数量（7天内）
	sevenDaysLater := time.Now().AddDate(0, 0, 7).UnixMilli()
	var expiringSoon int64
	if err := db.Model(&model.QuotaPackageRecord{}).
		Where("status = ? AND expires_at > 0 AND expires_at <= ?", types.QuotaPackageStatusActive, sevenDaysLater).
		Count(&expiringSoon).Error; err != nil {
		logger.Error("Failed to count expiring soon quota packages", "error", err)
		cosy.ErrHandler(c, err)
		return
	}

	// 2. 获取模块统计数据
	type ModuleStatsResult struct {
		Module     string  `json:"module"`
		Count      int64   `json:"count"`
		TotalQuota int64   `json:"total_quota"`
		TotalUsed  int64   `json:"total_used"`
		Available  int64   `json:"available"`
		UsageRate  float64 `json:"usage_rate"`
	}

	var moduleStatsResults []ModuleStatsResult
	if err := db.Raw(`
		SELECT
			module,
			COUNT(*) as count,
			SUM(quota) as total_quota,
			SUM(used) as total_used,
			SUM(quota - used) as available,
			CASE WHEN SUM(quota) > 0 THEN (SUM(used) * 100.0 / SUM(quota)) ELSE 0 END as usage_rate
		FROM quota_package_records
		WHERE status = ?
		GROUP BY module
	`, types.QuotaPackageStatusActive).Scan(&moduleStatsResults).Error; err != nil {
		logger.Error("Failed to get module statistics", "error", err)
		cosy.ErrHandler(c, err)
		return
	}

	// 3. 构建模块统计数据
	moduleStats := make([]QuotaPackageModuleStat, 0, len(moduleStatsResults))
	var totalCost float64

	for _, result := range moduleStatsResults {
		// 获取模块显示名称
		var name string
		switch result.Module {
		case "llm":
			name = "LLM服务"
		case "tts":
			name = "TTS服务"
		case "asr":
			name = "ASR服务"
		default:
			name = result.Module
		}

		// 从价格规则表获取真实的单位成本和单位
		var pricingRule model.PricingRule
		var unitCost float64
		var unit string

		// 先查找通用规则（model_name为空）
		err := db.Where("module = ? AND model_name = '' AND is_active = ?", result.Module, true).
			Order("priority DESC").
			First(&pricingRule).Error

		if err == nil {
			// 找到价格规则，使用真实的单位成本
			unitCost = pricingRule.GetEffectiveUnitPrice()
			unit = pricingRule.Unit
		} else {
			// 没有找到价格规则，使用默认值
			logger.Warn("No pricing rule found for module", "module", result.Module)
			unitCost = 0
			switch result.Module {
			case "llm":
				unit = "token"
			case "tts":
				unit = "character"
			case "asr":
				unit = "second"
			default:
				unit = "unit"
			}
		}

		// 计算模块成本
		moduleCost := float64(result.TotalUsed) * unitCost
		totalCost += moduleCost

		moduleStats = append(moduleStats, QuotaPackageModuleStat{
			Module:     result.Module,
			Name:       name,
			Count:      result.Count,
			TotalQuota: result.TotalQuota,
			TotalUsed:  result.TotalUsed,
			Available:  result.Available,
			UsageRate:  result.UsageRate,
			Unit:       unit,
			Cost:       moduleCost,
		})
	}

	// 4. 构建响应数据
	stats := QuotaPackageOverviewStats{
		Total:        total,
		Active:       active,
		Expired:      expired,
		Exhausted:    exhausted,
		Disabled:     disabled,
		ExpiringSoon: expiringSoon,
		TotalCost:    totalCost,
		ModuleStats:  moduleStats,
	}

	c.JSON(http.StatusOK, stats)
}
