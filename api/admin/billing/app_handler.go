package billing

import (
	"errors"
	"net/http"

	"git.uozi.org/uozi/potato-billing-api/internal/billing"
	"git.uozi.org/uozi/potato-billing-api/model"
	"git.uozi.org/uozi/potato-billing-api/query"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/logger"
)

// AppStatusResponse 应用状态响应结构
type AppStatusResponse struct {
	AppID     uint64                      `json:"app_id"`
	Status    string                      `json:"status"`
	Available bool                        `json:"available"`
	Quotas    []*model.QuotaPackageRecord `json:"quotas"`
	User      *model.User                 `json:"user,omitempty"`
	CreatedAt int64                       `json:"created_at"`
	UpdatedAt int64                       `json:"updated_at"`
}

// AppOverviewStats API Key概览统计响应结构
type AppOverviewStats struct {
	TotalApps       int64        `json:"total_apps"`        // 总应用数量
	ActiveApps      int64        `json:"active_apps"`       // 活跃应用数量
	BlockedApps     int64        `json:"blocked_apps"`      // 阻止应用数量
	AppsWithQuota   int64        `json:"apps_with_quota"`   // 有配额的应用数量
	AppsWithBalance int64        `json:"apps_with_balance"` // 用户有余额的应用数量
	TotalQuotaUsage float64      `json:"total_quota_usage"` // 总配额使用率
	AvgQuotaUsage   float64      `json:"avg_quota_usage"`   // 平均配额使用率
	ModuleStats     []ModuleStat `json:"module_stats"`      // 按模块统计
}

// ModuleStat 模块统计结构
type ModuleStat struct {
	Module      string  `json:"module"`       // 模块名称
	Name        string  `json:"name"`         // 模块显示名称
	AppCount    int64   `json:"app_count"`    // 应用数量
	ActiveCount int64   `json:"active_count"` // 活跃应用数量
	AvgUsage    float64 `json:"avg_usage"`    // 平均使用率
}

// RegisterAppAPI 初始化应用管理接口
func RegisterAppAPI(g *gin.RouterGroup) {
	g.GET("/apps", GetAppList)
	g.GET("/apps/:app_id", GetApp)
	g.PUT("/apps/:app_id", UpdateApp)
	g.DELETE("/apps/:app_id", DeleteApp)
	g.POST("/apps", CreateApp)
	g.POST("/apps/:app_id", UpdateApp)
	g.GET("/apps/overview/stats", GetAppOverviewStats)
	g.GET("/apps/:app_id/status", GetAppStatus)
}

// GetAppList 获取应用列表
func GetAppList(c *gin.Context) {
	core := cosy.Core[model.App](c)

	core.SetPreloads("User")

	core.SetEqual("user_id")

	core.PagingList()
}

// CreateApp 创建应用
func CreateApp(c *gin.Context) {
	var body model.App
	if err := c.ShouldBindJSON(&body); err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	billingService := billing.GetBillingService()
	err := billingService.GetAppService().CreateApp(c.Request.Context(), &body)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "应用创建成功"})
}

// UpdateAppRequest 更新应用请求结构
type UpdateAppRequest struct {
	Name    string `json:"name"`
	Comment string `json:"comment"`
	Status  string `json:"status"`
}

// GetApp 获取应用
func GetApp(c *gin.Context) {
	appID := cast.ToUint64(c.Param("app_id"))
	if appID == 0 {
		cosy.ErrHandler(c, errors.New("应用ID参数必需"))
		return
	}

	app, err := query.App.
		Preload(query.App.User).
		Where(query.App.ID.Eq(appID)).
		First()
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, app)
}

// UpdateApp 更新应用
func UpdateApp(c *gin.Context) {
	appID := cast.ToUint64(c.Param("app_id"))
	if appID == 0 {
		cosy.ErrHandler(c, errors.New("应用ID参数必需"))
		return
	}

	body := &UpdateAppRequest{}
	if err := c.ShouldBindJSON(body); err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	query.App.
		Where(query.App.ID.Eq(appID)).
		Updates(body)

	GetApp(c)
}

// DeleteApp 删除应用
func DeleteApp(c *gin.Context) {
	appID := cast.ToUint64(c.Param("app_id"))
	if appID == 0 {
		cosy.ErrHandler(c, errors.New("应用ID参数必需"))
		return
	}

	query.App.
		Where(query.App.ID.Eq(appID)).
		Delete()

	c.JSON(http.StatusOK, gin.H{"message": "Key deleted successfully"})
}

// GetAppStatus 获取应用状态
func GetAppStatus(c *gin.Context) {
	appID := cast.ToUint64(c.Param("app_id"))
	if appID == 0 {
		cosy.ErrHandler(c, errors.New("应用ID参数必需"))
		return
	}

	// 查询Key记录
	app, err := query.App.
		Preload(query.App.User).
		Where(query.App.ID.Eq(appID)).
		First()
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 查询该类型的专属配额信息
	var quotas []*model.QuotaPackageRecord
	err = query.QuotaPackageRecord.UnderlyingDB().
		Where("(api_key IN ? OR api_key IS NULL)", []string{app.APIKey, ""}).
		Where("user_id = ?", app.UserID).
		Find(&quotas).Error
	if err != nil {
		logger.Error("Failed to query quotas", "error", err, "key_id", app.ID)
		cosy.ErrHandler(c, err)
		return
	}

	// 计算是否可用（只检查对应类型的配额）
	available := app.Status == "ok"
	if len(quotas) > 0 {
		for _, quota := range quotas {
			if quota.Quota > 0 && quota.Used >= quota.Quota && app.User.Balance <= 0 {
				available = false
				break
			}
		}
	} else {
		// 没有该类型的配额，检查用户余额
		if app.User == nil || app.User.Balance <= 0 {
			available = false
		}
	}

	response := AppStatusResponse{
		AppID:     app.ID,
		Status:    app.Status,
		Available: available,
		Quotas:    quotas,
		User:      app.User,
		CreatedAt: app.CreatedAt,
		UpdatedAt: app.UpdatedAt,
	}

	c.JSON(http.StatusOK, response)
}

// GetAppOverviewStats 获取应用概览统计
func GetAppOverviewStats(c *gin.Context) {
	// 获取统计服务实例
	statService := billing.GetStatServiceInstance()
	if statService == nil {
		logger.Error("获取统计服务失败")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "统计服务不可用"})
		return
	}

	// 调用统计服务获取数据
	stats, err := statService.GetAppOverviewStats(c.Request.Context())
	if err != nil {
		logger.Error("获取应用概览统计失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取统计数据失败"})
		return
	}

	c.JSON(http.StatusOK, stats)
}
