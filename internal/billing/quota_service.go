package billing

import (
	"context"
	"fmt"
	"time"

	"git.uozi.org/uozi/potato-billing-api/model"
	"git.uozi.org/uozi/potato-billing-api/query"
	"git.uozi.org/uozi/potato-billing-api/settings"
	"git.uozi.org/uozi/potato-billing-api/types"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/logger"
	"gorm.io/gorm"
)

// QuotaService 资源包管理服务
type QuotaService struct {
	cacheService *CacheService
	config       *settings.BillingConfig
}

// NewQuotaService 创建资源包管理服务
func NewQuotaService(cacheService *CacheService, config *settings.BillingConfig) *QuotaService {
	return &QuotaService{
		cacheService: cacheService,
		config:       config,
	}
}

// TryQuotaBilling 尝试使用资源包计费
func (qs *QuotaService) TryQuotaBilling(tx *query.Query, userID uint64, apiKey, module, modelName string, usage int64) types.QuotaBillingResult {
	// 查找可用的资源包，按优先级排序：
	// 1. 指定API Key的资源包
	// 2. 指定模型名的资源包
	// 3. 通用资源包（不指定API Key）
	// 4. 按过期时间排序（先过期的先用）

	var quotaPackages []model.QuotaPackageRecord
	err := tx.QuotaPackageRecord.UnderlyingDB().
		Where("user_id = ?", userID).
		Where("module = ?", module).
		Where("(model_name = ? OR model_name IS NULL OR model_name = '')", modelName).
		Where("status = ?", types.QuotaPackageStatusActive).
		Where("used < quota").
		Where("(api_key = ? OR api_key IS NULL OR api_key = '')", apiKey).
		Order(
			fmt.Sprintf("CASE WHEN model_name = '%s' THEN 0 ELSE 1 END", modelName),
		).
		Order(
			fmt.Sprintf("CASE WHEN api_key = '%s' THEN 0 ELSE 1 END", apiKey),
		).
		Order(
			"CASE WHEN expires_at = 0 THEN 1 ELSE 0 END",
		).
		Order(
			"expires_at ASC",
		).
		Find(&quotaPackages).
		Error

	if err != nil || len(quotaPackages) == 0 {
		return types.QuotaBillingResult{Success: false, Message: "无可用资源包"}
	}

	// 尝试从资源包中扣减用量
	for _, pkg := range quotaPackages {
		if pkg.Available >= usage {
			// 扣减用量
			_, err = tx.QuotaPackageRecord.
				Where(query.QuotaPackageRecord.ID.Eq(pkg.ID)).
				Update(query.QuotaPackageRecord.Used, gorm.Expr("used + ?", usage))
			if err != nil {
				continue
			}

			// 检查是否用完，更新状态
			if pkg.Available == usage {
				_, err = tx.QuotaPackageRecord.
					Where(query.QuotaPackageRecord.ID.Eq(pkg.ID)).
					Update(query.QuotaPackageRecord.Status, types.QuotaPackageStatusExhausted)
				if err != nil {
					continue
				}
			}

			return types.QuotaBillingResult{
				Success:        true,
				QuotaPackageID: pkg.ID,
				Message:        "资源包计费成功",
			}
		}
	}

	return types.QuotaBillingResult{Success: false, Message: "资源包配额不足"}
}

// GetUserQuotaPackages 获取用户资源包列表
func (qs *QuotaService) GetUserQuotaPackages(ctx context.Context, userID uint64, module, status string, appID uint64) ([]model.QuotaPackageRecord, error) {
	db := cosy.UseDB(ctx).Where("user_id = ?", userID)

	if module != "" {
		db = db.Where("module = ?", module)
	}
	if status != "" {
		db = db.Where("status = ?", status)
	}
	if appID != 0 {
		db = db.Where("app_id = ?", appID)
	}

	var quotaPackages []model.QuotaPackageRecord
	err := db.Preload("User").
		Preload("Operator").
		Order("created_at DESC").
		Find(&quotaPackages).Error

	return quotaPackages, err
}

// CreateQuotaPackage 创建资源包
func (qs *QuotaService) CreateQuotaPackage(ctx context.Context, pkg *model.QuotaPackageRecord) error {
	// 检查用户是否存在
	db := cosy.UseDB(ctx)
	var userExists bool
	err := db.Model(&model.User{}).
		Select("1").
		Where("id = ?", pkg.UserID).
		First(&userExists).Error
	if err != nil {
		return fmt.Errorf("用户不存在")
	}

	// 如果指定了API Key，检查是否存在
	if pkg.AppID != 0 {
		var keyExists bool
		err := db.Model(&model.App{}).
			Select("1").
			Where("id = ? AND user_id = ?", pkg.AppID, pkg.UserID).
			First(&keyExists).Error
		if err != nil {
			return fmt.Errorf("API Key不存在或不属于该用户")
		}
	}

	// 设置默认值
	if pkg.Status == "" {
		pkg.Status = types.QuotaPackageStatusActive
	}
	if pkg.Type == "" {
		pkg.Type = types.QuotaPackageSourceAdmin
	}

	// 检查是否过期
	if pkg.ExpiresAt > 0 && pkg.ExpiresAt < time.Now().UnixMilli() {
		pkg.Status = types.QuotaPackageStatusExpired
	}

	return db.Create(pkg).Error
}

// UpdateQuotaPackage 更新资源包
func (qs *QuotaService) UpdateQuotaPackage(ctx context.Context, id uint64, updates map[string]interface{}) error {
	db := cosy.UseDB(ctx)

	// 获取原记录
	var pkg model.QuotaPackageRecord
	if err := db.First(&pkg, id).Error; err != nil {
		return err
	}

	// 更新记录
	err := db.Model(&pkg).Updates(updates).Error
	if err != nil {
		return err
	}

	// 清除相关缓存
	qs.invalidateQuotaCache(pkg.UserID, pkg.Module, pkg.AppID)

	return nil
}

// DisableQuotaPackage 禁用资源包
func (qs *QuotaService) DisableQuotaPackage(ctx context.Context, id uint64) error {
	return qs.UpdateQuotaPackage(ctx, id, map[string]interface{}{"status": types.QuotaPackageStatusDisabled})
}

// EnableQuotaPackage 启用资源包
func (qs *QuotaService) EnableQuotaPackage(ctx context.Context, id uint64) error {
	db := cosy.UseDB(ctx)

	// 获取资源包信息
	var pkg model.QuotaPackageRecord
	if err := db.First(&pkg, id).Error; err != nil {
		return err
	}

	// 检查状态
	status := types.QuotaPackageStatusActive
	if pkg.ExpiresAt > 0 && pkg.ExpiresAt < time.Now().UnixMilli() {
		status = types.QuotaPackageStatusExpired
	} else if pkg.Used >= pkg.Quota {
		status = types.QuotaPackageStatusExhausted
	}

	return qs.UpdateQuotaPackage(ctx, id, map[string]interface{}{"status": status})
}

// GetQuotaUsageStats 获取资源包使用统计
func (qs *QuotaService) GetQuotaUsageStats(ctx context.Context, userID uint64, module string) (map[string]interface{}, error) {
	db := cosy.UseDB(ctx)

	type QuotaStats struct {
		Status     string `json:"status"`
		Count      int64  `json:"count"`
		TotalQuota int64  `json:"total_quota"`
		TotalUsed  int64  `json:"total_used"`
		Available  int64  `json:"available"`
	}

	var stats []QuotaStats
	query := db.Model(&model.QuotaPackageRecord{}).
		Select("status, COUNT(*) as `count`, SUM(quota) as `total_quota`, SUM(used) as `total_used`, SUM(quota - used) as `available`").
		Where("user_id = ?", userID).
		Group("status")

	if module != "" {
		query = query.Where("module = ?", module)
	}

	err := query.Find(&stats).Error
	if err != nil {
		return nil, err
	}

	result := make(map[string]interface{})
	result["by_status"] = stats

	// 计算总计
	var totalCount, totalQuota, totalUsed, totalAvailable int64
	for _, stat := range stats {
		totalCount += stat.Count
		totalQuota += stat.TotalQuota
		totalUsed += stat.TotalUsed
		totalAvailable += stat.Available
	}

	result["total"] = map[string]interface{}{
		"count":     totalCount,
		"quota":     totalQuota,
		"used":      totalUsed,
		"available": totalAvailable,
	}

	return result, nil
}

// invalidateQuotaCache 清除资源包相关缓存
func (qs *QuotaService) invalidateQuotaCache(userID uint64, module string, appID uint64) {
	// 清除用户相关的缓存
	if appID != 0 {
		if err := qs.cacheService.InvalidateAppStatus(appID); err != nil {
			logger.Error("清除应用状态缓存失败", "error", err, "app_id", appID)
		}
	}

	// 这里可以添加更多缓存清理逻辑
	logger.Debug("资源包缓存已清除", "user_id", userID, "module", module, "app_id", appID)
}
