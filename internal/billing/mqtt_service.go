package billing

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"net/url"
	"os"
	"strconv"
	"time"

	"git.uozi.org/uozi/potato-billing-api/query"
	"git.uozi.org/uozi/potato-billing-api/settings"
	"git.uozi.org/uozi/potato-billing-api/types"
	"github.com/eclipse/paho.golang/autopaho"
	"github.com/eclipse/paho.golang/paho"
	"github.com/uozi-tech/cosy/logger"
)

// MQTTService MQTT服务结构体 - 仅负责消息通信
type MQTTService struct {
	client              *autopaho.ConnectionManager
	ctx                 context.Context
	billingEngine       *BillingEngine // 业务逻辑委托给计费引擎
	TopicAppListRequest string         // 应用列表请求主题
	TopicAppListUpdate  string         // 应用列表更新主题
	TopicUsageReport    string         // 用量上报主题
}

// UsageReport 用量上报消息结构
type UsageReport struct {
	AppID     uint64                 `json:"app_id"`    // 应用ID
	APIKey    string                 `json:"api_key"`   // API Key
	Module    string                 `json:"module"`    // 'llm' | 'tts' | 'asr'
	Model     string                 `json:"model"`     // 模型名称
	Usage     int64                  `json:"usage"`     // 用量
	Metadata  map[string]interface{} `json:"metadata"`  // 元数据
	Timestamp int64                  `json:"timestamp"` // 使用时间戳
}

// AppStatusUpdate 应用状态更新消息
type AppStatusUpdate struct {
	AppID  uint64 `json:"app_id"`  // 应用ID
	APIKey string `json:"api_key"` // API Key
	Status string `json:"status"`  // 'ok' | 'blocked'
	Reason string `json:"reason"`  // 状态更新原因
}

// AppStatusBatchUpdate 批量应用状态更新消息
type AppStatusBatchUpdate struct {
	Updates   []AppStatusUpdate `json:"updates"`
	Timestamp int64             `json:"timestamp"`
}

// NewMQTTService 创建新的MQTT服务
func NewMQTTService(ctx context.Context, brokerURL string) (*MQTTService, error) {
	u, err := url.Parse(brokerURL)
	if err != nil {
		return nil, fmt.Errorf("解析MQTT broker URL失败: %w", err)
	}

	service := &MQTTService{
		ctx:                 ctx,
		TopicAppListRequest: "billing/keys/request",
		TopicAppListUpdate:  "billing/keys/update",
		TopicUsageReport:    "billing/report",
	}

	clientID := generateClientID()
	logger.Info("Creating MQTT client", "client_id", clientID)

	cliCfg := autopaho.ClientConfig{
		ServerUrls:                    []*url.URL{u},
		KeepAlive:                     30,
		CleanStartOnInitialConnection: false, // 不清除旧会话
		SessionExpiryInterval:         7200,  // 设置会话过期时间为 2 小时，断线后 2 小时内消息都能缓存

		TlsCfg: &tls.Config{
			InsecureSkipVerify: true,
		},

		ConnectUsername: settings.Billing.MQTTUsername,
		ConnectPassword: []byte(settings.Billing.MQTTPassword),

		OnConnectionUp: func(cm *autopaho.ConnectionManager, connAck *paho.Connack) {
			logger.Info("MQTT connection established")

			// 订阅用量上报主题
			if _, err := cm.Subscribe(context.Background(), &paho.Subscribe{
				Subscriptions: []paho.SubscribeOptions{
					{Topic: service.TopicUsageReport, QoS: 1},
					{Topic: service.TopicAppListRequest, QoS: 1},
				},
			}); err != nil {
				logger.Error("Failed to subscribe to topics", "error", err)
			} else {
				logger.Info("Successfully subscribed to topics")
			}

			// 连接成功之后，发布应用列表
			service.handleAppStatusUpdate()
		},

		OnConnectError: func(err error) {
			logger.Error("MQTT 连接失败：", err)
		},

		ClientConfig: paho.ClientConfig{
			ClientID: clientID,
			OnPublishReceived: []func(paho.PublishReceived) (bool, error){
				service.handleMessage,
			},
			OnClientError: func(err error) {
				logger.Error("MQTT 客户端错误：", err)
			},
			OnServerDisconnect: func(d *paho.Disconnect) {
				if d.Properties != nil {
					logger.Warn("服务器请求断开连接：", d.Properties.ReasonString)
				} else {
					logger.Warn("服务器请求断开连接：", d.ReasonCode)
				}
			},
		},
	}

	client, err := autopaho.NewConnection(context.Background(), cliCfg)
	if err != nil {
		return nil, fmt.Errorf("创建 MQTT 连接失败: %w", err)
	}

	service.client = client
	return service, nil
}

// SetBillingEngine 设置计费引擎（用于依赖注入）
func (s *MQTTService) SetBillingEngine(billingEngine *BillingEngine) {
	s.billingEngine = billingEngine
}

// handleMessage 处理接收到的MQTT消息
func (s *MQTTService) handleMessage(pr paho.PublishReceived) (bool, error) {
	topic := pr.Packet.Topic
	payload := pr.Packet.Payload

	logger.Info("Received MQTT message", "topic", topic, "payload_size", len(payload))

	switch topic {
	case s.TopicUsageReport:
		return s.handleUsageReport(payload)
	case s.TopicAppListRequest:
		return s.handleAppStatusUpdate()
	default:
		logger.Warn("Received message on unknown topic", "topic", topic)
	}

	return true, nil
}

// handleUsageReport 处理用量上报消息 - 仅负责消息解析和委托
func (s *MQTTService) handleUsageReport(payload []byte) (bool, error) {
	var report UsageReport
	if err := json.Unmarshal(payload, &report); err != nil {
		logger.Error("解析用量上报失败：", "error", err, "payload", string(payload))
		return true, fmt.Errorf("解析用量上报失败: %w", err)
	}

	// 验证消息格式
	if report.APIKey == "" || report.Module == "" || report.Usage <= 0 {
		logger.Errorf("无效的用量上报(app_id: %d, module: %s, usage: %d)", report.AppID, report.Module, report.Usage)
		return true, fmt.Errorf("无效的用量上报格式")
	}

	// 将业务逻辑委托给计费引擎
	if s.billingEngine != nil {
		billingRequest := types.BillingRequest{
			AppID:     report.AppID,
			APIKey:    report.APIKey,
			Module:    report.Module,
			ModelName: report.Model,
			Usage:     report.Usage,
			Metadata:  report.Metadata,
		}

		result, err := s.billingEngine.ProcessBilling(billingRequest)
		if err != nil {
			logger.Error("计费处理失败", "error", err, "report", report)
			// 不返回错误，避免影响消息确认
		} else {
			logger.Info("用量计费处理成功",
				"api_key", report.APIKey,
				"module", report.Module,
				"cost", result.Cost,
				"billing_type", result.BillingType)
		}
	} else {
		logger.Warn("计费引擎未设置，跳过计费处理")
	}

	return true, nil
}

// handleAppStatusUpdate 处理应用状态更新消息
func (s *MQTTService) handleAppStatusUpdate() (bool, error) {
	apps, err := query.App.Where(query.App.Status.In(types.AppStatusOk, types.AppStatusBlocked)).Find()
	if err != nil {
		logger.Error("获取应用列表失败", "error", err)
		return true, fmt.Errorf("获取应用列表失败: %w", err)
	}

	updates := make([]AppStatusUpdate, 0, len(apps))
	for _, app := range apps {
		updates = append(updates, AppStatusUpdate{
			AppID:  app.ID,
			APIKey: app.APIKey,
			Status: app.Status,
			Reason: "",
		})
	}

	logger.Debug("App status update", "updates", updates)

	s.PublishAppStatusBatch(updates)

	return true, nil
}

// PublishAppStatus 发布应用状态更新（单个）
func (s *MQTTService) PublishAppStatus(key, status, reason string) {
	updates := []AppStatusUpdate{
		{
			APIKey: key,
			Status: status,
			Reason: reason,
		},
	}
	s.PublishAppStatusBatch(updates)
}

// PublishAppStatusBatch 批量发布应用状态更新
func (s *MQTTService) PublishAppStatusBatch(updates []AppStatusUpdate) {
	if len(updates) == 0 {
		return
	}

	topic := s.TopicAppListUpdate
	message := AppStatusBatchUpdate{
		Updates:   updates,
		Timestamp: time.Now().UnixMilli(),
	}

	payload, err := json.Marshal(message)
	if err != nil {
		logger.Error("序列化应用状态批量消息失败", "error", err)
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err = s.client.Publish(ctx, &paho.Publish{
		QoS:     1,
		Topic:   topic,
		Payload: payload,
	})

	if err != nil {
		logger.Error("发布应用状态批量消息失败", "error", err, "topic", topic)
	} else {
		logger.Info("应用状态批量更新发布成功",
			"topic", topic,
			"count", len(updates),
		)
	}
}

// AwaitConnection 等待MQTT连接建立
func (s *MQTTService) AwaitConnection(ctx context.Context) error {
	return s.client.AwaitConnection(ctx)
}

// Disconnect 断开MQTT连接
func (s *MQTTService) Disconnect(ctx context.Context) error {
	return s.client.Disconnect(ctx)
}

// generateClientID 生成唯一的客户端ID
func generateClientID() string {
	hostname, _ := os.Hostname()
	return "billing-system-" + hostname + "-" + strconv.FormatInt(time.Now().Unix(), 10)
}
