## 1. 产品概述

基于现有管理端架构的计费系统用户端应用，为开发者提供简洁易用的API Key管理、计费查看和产品展示功能。采用Vue 3 + TypeScript + Vite技术栈，复用shadcn-vue组件库，提供专业的开发者服务平台。

## 2. 核心功能

### 2.1 用户角色

| 角色 | 注册方式 | 核心权限 |
|------|----------|----------|
| 开发者用户 | 邮箱注册 | 可创建应用、管理API Key、查看计费信息 |

### 2.2 功能模块

我们的计费系统用户端包含以下主要页面：

1. **产品落地页**：Hero展示区、服务介绍、定价信息、快速开始指南
2. **控制台首页**：统计概览、快速操作、最近活动、服务状态
3. **应用管理页**：应用列表、应用详情、API Key管理
4. **计费查看页**：费用统计、趋势图表、使用量分析
5. **认证页面**：用户登录、注册功能

### 2.3 页面详情

| 页面名称 | 模块名称 | 功能描述 |
|----------|----------|----------|
| 产品落地页 | Hero展示区 | 展示主标题、核心价值主张、CTA按钮引导用户注册 |
| 产品落地页 | 服务介绍区 | 介绍LLM、TTS、ASR三大AI服务的特性和优势 |
| 产品落地页 | 定价展示区 | 透明展示各服务的定价策略和计费方式 |
| 产品落地页 | 快速开始区 | 提供API接入指南和示例代码 |
| 控制台首页 | 统计卡片 | 显示应用数量、API Key数量、本月费用、本月使用量 |
| 控制台首页 | 快速操作 | 创建应用、查看文档、联系支持的快捷入口 |
| 控制台首页 | 最近活动 | 展示最近的API使用情况和费用变化趋势 |
| 应用管理页 | 应用列表 | 卡片式展示用户创建的应用，包含基本信息和使用统计 |
| 应用管理页 | 应用详情 | 应用信息编辑、API Key完整管理、详细统计图表 |
| 应用管理页 | API Key管理 | 生成、查看、启用/禁用、重新生成API Key功能 |
| 计费查看页 | 时间控制器 | 支持日/周/月视图切换和自定义日期范围选择 |
| 计费查看页 | 趋势图表 | 费用趋势折线图、分服务堆叠柱状图、使用量分布饼图 |
| 计费查看页 | 费用明细 | 按时间聚合的费用汇总信息（保护隐私，不显示具体调用记录） |
| 认证页面 | 用户登录 | 邮箱密码登录、记住登录状态、忘记密码功能 |
| 认证页面 | 用户注册 | 邮箱注册、密码设置、邮箱验证流程 |

## 3. 核心流程

**开发者用户流程**：
1. 访问产品落地页了解服务 → 注册开发者账号 → 登录进入控制台
2. 在控制台查看整体使用情况 → 创建新应用 → 生成API Key
3. 在应用详情页管理API Key → 查看应用使用统计 → 监控配额使用情况
4. 在计费页面查看费用趋势 → 分析使用成本 → 优化API调用策略

```mermaid
graph TD
    A[产品落地页] --> B[用户注册]
    B --> C[用户登录]
    C --> D[控制台首页]
    D --> E[应用管理页]
    E --> F[应用详情页]
    F --> G[API Key管理]
    D --> H[计费查看页]
    H --> I[费用分析]
```

## 4. 用户界面设计

### 4.1 设计风格

- **主色调**：蓝色系 (#3B82F6) 作为主色，灰色系 (#6B7280) 作为辅助色
- **按钮样式**：圆角按钮设计，支持多种状态（默认、悬停、禁用）
- **字体**：Inter字体，标题使用16-24px，正文使用14-16px
- **布局风格**：卡片式布局，顶部导航栏设计，响应式网格系统
- **图标风格**：使用Lucide图标库，保持简洁现代的视觉风格

### 4.2 页面设计概览

| 页面名称 | 模块名称 | UI元素 |
|----------|----------|--------|
| 产品落地页 | Hero展示区 | 全屏背景渐变、大标题Typography、CTA按钮动画效果 |
| 产品落地页 | 服务介绍区 | 三栏网格布局、服务图标、特性列表、悬停交互效果 |
| 控制台首页 | 统计卡片 | 4列网格布局、数字动画、趋势图标、颜色编码状态 |
| 应用管理页 | 应用列表 | 响应式卡片网格、悬停阴影效果、状态徽章、操作按钮组 |
| 应用详情页 | API Key管理 | 表格布局、遮掩显示、一键复制、状态切换开关 |
| 计费查看页 | 趋势图表 | Chart.js图表库、自适应容器、交互式图例、数据筛选器 |

### 4.3 响应式设计

采用移动端优先的响应式设计策略，支持桌面端、平板端和移动端的完美适配。在移动端使用折叠式导航菜单，优化触摸交互体验。