import type { RechargeRecord, RechargeStatus, RechargeType } from '@billing/common'
import { extendCurdApi, http, useCurdApi } from '@uozi-admin/request'

// 创建充值请求接口
export interface CreateRechargeRequest extends Pick<
  RechargeRecord,
  'user_id'
  | 'amount'
  | 'type'
  | 'description'
  | 'trade_no'
> { }

// 充值统计响应接口
export interface RechargeStatsResponse {
  today_amount: number
  month_amount: number
  total_count: number
  average_amount: number
  pending_amount: number
  completed_amount: number
  failed_amount: number
  total_users: number
  active_users: number
  total_balance: number
}

// 用户余额信息接口
export interface UserBalanceInfo {
  id: string
  name: string
  email: string
  balance: number
  last_recharge: string
}

export const rechargeApi = extendCurdApi(useCurdApi<RechargeRecord>('/admin/billing/recharge_records'), {
  recharge: async (data: CreateRechargeRequest) => {
    return await http.post('/admin/billing/recharge', data)
  },
  getStats: async (): Promise<RechargeStatsResponse> => {
    return await http.get('/admin/billing/recharge/stats')
  },
  getUserBalances: async (): Promise<UserBalanceInfo[]> => {
    return await http.get('/admin/billing/recharge/user-balances')
  },
})
