<script setup lang="ts">
import type { Api<PERSON><PERSON> } from '@billing/common'
import {
  Badge,
  Button,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@billing/ui'
import {
  Copy,
  Key,
  RefreshCw,
} from 'lucide-vue-next'
import { toast } from 'vue-sonner'
import { formatDate } from '@/utils/format'

interface Props {
  userKeys: ApiKey[]
  loading?: boolean
}

interface Emits {
  (e: 'refresh'): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
})

const emit = defineEmits<Emits>()

// 复制API Key
async function copyApiKey(key: string) {
  try {
    await navigator.clipboard.writeText(key)
    toast.success('复制成功', {
      description: 'API Key已复制到剪贴板',
    })
  }
  catch (error) {
    toast.error('复制失败', {
      description: '请手动复制',
    })
  }
}

// 刷新数据
function handleRefresh() {
  emit('refresh')
}
</script>

<template>
  <Card>
    <CardHeader>
      <div class="flex items-center justify-between">
        <CardTitle class="flex items-center gap-2">
          <Key class="w-5 h-5" />
          API Keys ({{ userKeys.length }})
        </CardTitle>
        <Button
          variant="outline"
          size="sm"
          :disabled="loading"
          @click="handleRefresh"
        >
          <RefreshCw
            class="w-4 h-4"
            :class="[{ 'animate-spin': loading }]"
          />
        </Button>
      </div>
    </CardHeader>
    <CardContent>
      <div
        v-if="userKeys.length === 0"
        class="text-center py-8 text-gray-500"
      >
        <Key class="w-12 h-12 mx-auto mb-4 opacity-50" />
        <p>暂无关联的 API Key</p>
      </div>
      <div
        v-else
        class="space-y-3"
      >
        <div
          v-for="key in userKeys"
          :key="key.id"
          class="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
        >
          <div class="flex-1">
            <div class="flex items-center gap-2 mb-1">
              <code class="font-mono text-sm bg-gray-100 px-2 py-1 rounded">
                {{ key.api_key.substring(0, 4) }}********{{ key.api_key.substring(key.api_key.length - 4) }}
              </code>
              <Button
                variant="ghost"
                size="sm"
                @click="copyApiKey(key.api_key)"
              >
                <Copy class="w-3 h-3" />
              </Button>
            </div>
            <div class="flex items-center gap-4 text-xs text-gray-500">
              <span>{{ key.name || '未命名' }}</span>
              <span>创建于{{ formatDate(key.created_at) }}</span>
            </div>
          </div>
          <Badge :variant="key.status === 'ok' ? 'default' : 'secondary'">
            {{ key.status === 'ok' ? '正常' : '阻止' }}
          </Badge>
        </div>
      </div>
    </CardContent>
  </Card>
</template>
