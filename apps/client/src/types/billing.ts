export interface BillingStats {
  total_cost: number
  monthly_cost: number
  daily_average_cost: number
  total_usage: number
  monthly_usage: number
  applications_count: number
  api_keys_count: number
}

export interface UsageTrend {
  date: string
  cost: number
  usage: number
  llm_usage: number
  tts_usage: number
  asr_usage: number
}

export interface ServiceUsage {
  service: 'llm' | 'tts' | 'asr'
  usage: number
  cost: number
  percentage: number
}

export interface BillingPeriod {
  start_date: string
  end_date: string
  total_cost: number
  total_usage: number
  service_breakdown: ServiceUsage[]
}

export interface BillingQuery {
  period: 'day' | 'week' | 'month' | 'custom'
  start_date?: string
  end_date?: string
  application_id?: number
}
