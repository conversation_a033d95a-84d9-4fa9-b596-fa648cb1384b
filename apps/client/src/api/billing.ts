import type {
  BillingPeriod,
  BillingQuery,
  BillingStats,
  ServiceUsage,
  UsageTrend,
} from '@/types/billing'
import { http } from '@uozi-admin/request'

export const billingApi = {
  // 获取计费统计概览
  getBillingStats: (): Promise<BillingStats> => {
    return http.get('/client/billing/stats')
  },

  // 获取使用趋势数据
  getUsageTrends: (query: BillingQuery): Promise<UsageTrend[]> => {
    return http.get('/client/billing/trends', { params: query })
  },

  // 获取服务使用分布
  getServiceUsage: (query: BillingQuery): Promise<ServiceUsage[]> => {
    return http.get('/client/billing/services', { params: query })
  },

  // 获取计费周期数据
  getBillingPeriods: (query: BillingQuery): Promise<BillingPeriod[]> => {
    return http.get('/client/billing/periods', { params: query })
  },

  // 获取应用的计费数据
  getApplicationBilling: (applicationId: number, query: BillingQuery): Promise<{
    stats: BillingStats
    trends: UsageTrend[]
    services: ServiceUsage[]
  }> => {
    return http.get(`/client/applications/${applicationId}/billing`, { params: query })
  },

  // 导出计费报告
  exportBillingReport: (query: BillingQuery): Promise<Blob> => {
    return http.get('/client/billing/export', {
      params: query,
      responseType: 'blob',
    })
  },
}
