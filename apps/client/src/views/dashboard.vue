<script setup lang="ts">
import {
  Badge,
  Button,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Skeleton,
} from '@billing/ui'
import {
  ArcElement,
  CategoryScale,
  Chart as ChartJS,
  Legend,
  LinearScale,
  LineElement,
  PointElement,
  Title,
  Tooltip,
} from 'chart.js'
import {
  BarChart3,
  DollarSign,
  FileText,
  HelpCircle,
  Key,
  Layers,
  Plus,
  RefreshCw,
  Zap,
} from 'lucide-vue-next'
import { computed, onMounted, reactive, ref } from 'vue'
import {
  Doughnut,
  Line,
} from 'vue-chartjs'
import { toast } from 'vue-sonner'
import { appApi, billingApi } from '@/api'
import ClientLayout from '@/layouts/ClientLayout.vue'
import { useUserStore } from '@/store'

// 注册Chart.js组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
)

const userStore = useUserStore()
const loading = ref(false)
const trendPeriod = ref('7d')

// 统计数据
const stats = reactive({
  monthly_usage: 0,
  monthly_growth: 0,
  monthly_cost: 0,
  cost_growth: 0,
  application_count: 0,
  api_key_count: 0,
  active_api_keys: 0,
})

// 趋势数据
const trendData = reactive({
  labels: [],
  datasets: [],
})

// 服务分布数据
const serviceData = reactive({
  labels: [],
  datasets: [],
})

// 最近应用
const recentApplications = ref([])

// 图表配置
const trendChartData = computed(() => ({
  labels: trendData.labels,
  datasets: [
    {
      label: 'Token使用量',
      data: trendData.datasets[0]?.data || [],
      borderColor: 'rgb(59, 130, 246)',
      backgroundColor: 'rgba(59, 130, 246, 0.1)',
      tension: 0.4,
    },
  ],
}))

const trendChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false,
    },
  },
  scales: {
    y: {
      beginAtZero: true,
    },
  },
} as any

const serviceChartData = computed(() => ({
  labels: serviceData.labels,
  datasets: [
    {
      data: serviceData.datasets[0]?.data || [],
      backgroundColor: [
        'rgba(59, 130, 246, 0.8)',
        'rgba(16, 185, 129, 0.8)',
        'rgba(139, 92, 246, 0.8)',
        'rgba(245, 158, 11, 0.8)',
      ],
      borderWidth: 0,
    },
  ],
}))

const serviceChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom' as const,
    },
  },
} as any

// 格式化数字
function formatNumber(num: number) {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`
  }
  if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`
  }
  return num.toString()
}

// 格式化货币
function formatCurrency(amount: number) {
  return amount.toFixed(2)
}

// 加载统计数据
async function loadStats() {
  try {
    const data = await billingApi.getBillingStats()
    Object.assign(stats, data)
  }
  catch (error) {
    console.error('Failed to load stats:', error)
  }
}

// 加载趋势数据
async function loadTrendData() {
  try {
    const data = await billingApi.getUsageTrends({ period: trendPeriod.value as 'day' | 'week' | 'month' | 'custom' })
    Object.assign(trendData, data)
  }
  catch (error) {
    console.error('Failed to load trend data:', error)
  }
}

// 加载服务分布数据
async function loadServiceData() {
  try {
    const data = await billingApi.getServiceUsage({ period: 'month' })
    Object.assign(serviceData, data)
  }
  catch (error) {
    console.error('Failed to load service data:', error)
  }
}

// 加载最近应用
async function loadRecentApplications() {
  try {
    const data = await appApi.getList({
      page_size: 5,
    })
    recentApplications.value = Array.isArray(data) ? data.slice(0, 5) : []
  }
  catch (error) {
    console.error('Failed to load recent applications:', error)
  }
}

// 刷新数据
async function refreshData() {
  loading.value = true
  try {
    await Promise.all([
      loadStats(),
      loadTrendData(),
      loadServiceData(),
      loadRecentApplications(),
    ])
    toast.success('数据已刷新')
  }
  catch (error) {
    toast.error('刷新失败，请重试')
  }
  finally {
    loading.value = false
  }
}

// 初始化数据
onMounted(() => {
  refreshData()
})
</script>

<template>
  <ClientLayout>
    <div class="space-y-6">
      <!-- 页面标题 -->
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">
            控制台
          </h1>
          <p class="text-gray-600">
            欢迎回来，{{ userStore.user?.name || '用户' }}！
          </p>
        </div>
        <div class="flex items-center space-x-3">
          <Button
            :disabled="loading"
            variant="outline"
            @click="refreshData"
          >
            <RefreshCw
              class="w-4 h-4"
              :class="[{ 'animate-spin': loading }]"
            />
            刷新
          </Button>
          <Button
            as-child
            class="bg-blue-600"
          >
            <RouterLink to="/applications/create">
              <Plus class="w-4 h-4" />
              创建应用
            </RouterLink>
          </Button>
        </div>
      </div>

      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card class="px-0 py-4">
          <CardContent>
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                  <Zap class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <div class="text-sm font-medium text-muted-foreground truncate">
                  本月使用量
                </div>
                <div class="text-lg font-medium">
                  {{ formatNumber(stats.monthly_usage) }} Tokens
                </div>
              </div>
            </div>
            <div class="mt-4 pt-4 border-t">
              <div class="text-sm">
                <Badge
                  variant="secondary"
                  class="text-green-600"
                >
                  +{{ stats.monthly_growth }}%
                </Badge>
                <span class="text-muted-foreground ml-2">较上月</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card class="px-0 py-4">
          <CardContent>
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                  <DollarSign class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <div class="text-sm font-medium text-muted-foreground truncate">
                  本月费用
                </div>
                <div class="text-lg font-medium">
                  ¥{{ formatCurrency(stats.monthly_cost) }}
                </div>
              </div>
            </div>
            <div class="mt-4 pt-4 border-t">
              <div class="text-sm">
                <Badge
                  variant="secondary"
                  :class="{
                    'text-red-600': stats.cost_growth < 0,
                    'text-green-600': stats.cost_growth > 0,
                  }"
                >
                  {{ stats.cost_growth > 0 ? '+' : '' }}{{ stats.cost_growth }}%
                </Badge>
                <span class="text-muted-foreground ml-2">较上月</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card class="px-0 py-4">
          <CardContent>
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                  <Layers class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <div class="text-sm font-medium text-muted-foreground truncate">
                  应用数量
                </div>
                <div class="text-lg font-medium">
                  {{ stats.application_count }}
                </div>
              </div>
            </div>
            <div class="mt-4 pt-4 border-t">
              <div class="text-sm">
                <RouterLink
                  to="/applications"
                  class="text-primary hover:text-primary/80 font-medium"
                >
                  查看全部
                </RouterLink>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card class="px-0 py-4">
          <CardContent>
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                  <Key class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <div class="text-sm font-medium text-muted-foreground truncate">
                  API Keys
                </div>
                <div class="text-lg font-medium">
                  {{ stats.api_key_count }}
                </div>
              </div>
            </div>
            <div class="mt-4 pt-4 border-t">
              <div class="text-sm">
                <Badge
                  variant="secondary"
                  class="text-green-600"
                >
                  {{ stats.active_api_keys }}
                </Badge>
                <span class="text-muted-foreground ml-2">个活跃</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- 图表区域 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 使用趋势图 -->
        <Card>
          <CardHeader>
            <div class="flex items-center justify-between">
              <CardTitle>使用趋势</CardTitle>
              <Select
                v-model="trendPeriod"
                @update:model-value="loadTrendData"
              >
                <SelectTrigger class="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7d">
                    最近7天
                  </SelectItem>
                  <SelectItem value="30d">
                    最近30天
                  </SelectItem>
                  <SelectItem value="90d">
                    最近90天
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardHeader>
          <CardContent>
            <div class="h-64">
              <Line
                v-if="trendData.labels.length > 0"
                :data="trendChartData"
                :options="trendChartOptions"
              />
              <div
                v-else
                class="flex items-center justify-center h-full text-gray-500"
              >
                暂无数据
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- 服务分布图 -->
        <Card>
          <CardHeader>
            <CardTitle>服务使用分布</CardTitle>
            <CardDescription>各类AI服务的使用占比</CardDescription>
          </CardHeader>
          <CardContent>
            <div class="h-64">
              <Doughnut
                v-if="serviceData.labels.length > 0"
                :data="serviceChartData"
                :options="serviceChartOptions"
              />
              <div
                v-else
                class="flex items-center justify-center h-full text-gray-500"
              >
                暂无数据
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- 最近活动 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 最近应用 -->
        <Card>
          <CardHeader>
            <div class="flex items-center justify-between">
              <CardTitle>最近应用</CardTitle>
              <Button
                variant="outline"
                size="sm"
                as-child
              >
                <RouterLink to="/applications">
                  查看全部
                </RouterLink>
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div
              v-if="recentApplications.length === 0"
              class="text-center py-8"
            >
              <Layers class="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 class="mt-2 text-sm font-medium">
                暂无应用
              </h3>
              <p class="mt-1 text-sm text-muted-foreground">
                创建您的第一个应用开始使用AI服务
              </p>
              <div class="mt-6">
                <Button as-child>
                  <RouterLink to="/applications/create">
                    <Plus class="w-4 h-4 mr-2" />
                    创建应用
                  </RouterLink>
                </Button>
              </div>
            </div>

            <div
              v-else
              class="space-y-4"
            >
              <div
                v-for="app in recentApplications"
                :key="app.id"
                class="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
              >
                <div class="flex items-center space-x-3">
                  <div class="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Layers class="w-5 h-5 text-primary" />
                  </div>
                  <div>
                    <h4 class="font-medium">
                      {{ app.name }}
                    </h4>
                    <p class="text-sm text-muted-foreground">
                      {{ app.description }}
                    </p>
                  </div>
                </div>
                <div class="text-right">
                  <div class="text-sm font-medium">
                    {{ formatNumber(app.stats?.month_calls || 0) }}
                  </div>
                  <div class="text-xs text-muted-foreground">
                    本月调用
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- 快速操作 -->
        <Card>
          <CardHeader>
            <CardTitle>快速操作</CardTitle>
            <CardDescription>常用功能快速入口</CardDescription>
          </CardHeader>
          <CardContent>
            <div class="grid grid-cols-2 gap-4">
              <Button
                variant="outline"
                class="h-20 flex-col"
                as-child
              >
                <RouterLink to="/applications/create">
                  <Plus class="w-6 h-6 mb-2" />
                  创建应用
                </RouterLink>
              </Button>
              <Button
                variant="outline"
                class="h-20 flex-col"
                as-child
              >
                <RouterLink to="/billing">
                  <BarChart3 class="w-6 h-6 mb-2" />
                  查看账单
                </RouterLink>
              </Button>
              <Button
                variant="outline"
                class="h-20 flex-col"
                as-child
              >
                <RouterLink to="/docs">
                  <FileText class="w-6 h-6 mb-2" />
                  API文档
                </RouterLink>
              </Button>
              <Button
                variant="outline"
                class="h-20 flex-col"
                as-child
              >
                <RouterLink to="/help">
                  <HelpCircle class="w-6 h-6 mb-2" />
                  帮助中心
                </RouterLink>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  </ClientLayout>
</template>
