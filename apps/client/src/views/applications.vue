<script setup lang="ts">
import {
  Badge,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  Input,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Switch,
  Textarea,
} from '@billing/ui'
import {
  Copy,
  Edit,
  Key,
  Layers,
  Plus,
  RefreshCw,
  RotateCcw,
  Search,
  Trash2,
} from 'lucide-vue-next'
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { toast } from 'vue-sonner'
import { appApi } from '@/api'
import ClientLayout from '@/layouts/ClientLayout.vue'

const loading = ref(false)
const submitting = ref(false)
const searchQuery = ref('')
const statusFilter = ref('')
const sortBy = ref('created_at')

// 模态框状态
const showCreateModal = ref(false)
const showEditModal = ref(false)
const showApiKeysModal = ref(false)

// 应用数据
const applications = ref([])
const selectedApplication = ref(null)
const apiKeys = ref([])

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0,
})

// 应用表单
const applicationForm = reactive({
  name: '',
  comment: '',
})

// 过滤后的应用列表
const filteredApplications = computed(() => {
  let filtered = applications.value

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(
      app =>
        app.name.toLowerCase().includes(query)
        || app.description.toLowerCase().includes(query),
    )
  }

  // 状态过滤
  if (statusFilter.value) {
    filtered = filtered.filter(app => app.status === statusFilter.value)
  }

  // 排序
  filtered.sort((a, b) => {
    if (sortBy.value === 'name') {
      return a.name.localeCompare(b.name)
    }
    if (sortBy.value === 'usage') {
      return (b.stats?.month_calls || 0) - (a.stats?.month_calls || 0)
    }
    return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
  })

  return filtered
})

// 格式化数字
function formatNumber(num: number) {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`
  }
  if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`
  }
  return num.toString()
}

// 格式化货币
function formatCurrency(amount: number) {
  return amount.toFixed(2)
}

// 格式化日期
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 加载应用列表
async function loadApplications() {
  loading.value = true
  try {
    const res = await appApi.getList({
      page: pagination.page,
      page_size: pagination.pageSize,
    })
    applications.value = Array.isArray(res.data) ? res.data : []
    pagination.total = res.pagination.total
  }
  catch (error) {
    toast.error('加载应用列表失败')
  }
  finally {
    loading.value = false
  }
}

// 创建应用
async function submitApplication() {
  submitting.value = true
  try {
    if (showCreateModal.value) {
      await appApi.createApplication(applicationForm)
      toast.success('应用创建成功')
    }
    else {
      await appApi.updateApplication(selectedApplication.value.id, applicationForm)
      toast.success('应用更新成功')
    }
    closeModals()
    loadApplications()
  }
  catch (error) {
    toast.error(showCreateModal.value ? '创建失败' : '更新失败')
  }
  finally {
    submitting.value = false
  }
}

// 编辑应用
function editApplication(app) {
  selectedApplication.value = app
  applicationForm.name = app.name
  applicationForm.comment = app.comment || ''
  showEditModal.value = true
}

// 删除应用
async function deleteApplication(app) {
  if (!confirm(`确定要删除应用【${app.name}】吗？此操作不可恢复。`)) {
    return
  }

  try {
    await appApi.deleteApplication(app.id)
    toast.success('应用删除成功')
    loadApplications()
  }
  catch (error) {
    toast.error('删除失败')
  }
}

// 查看API Keys
async function viewApiKeys(app) {
  selectedApplication.value = app
}

// 创建API Key
async function createApiKey() {
  const name = prompt('请输入API Key名称：（必填）')
  if (!name)
    return

  try {
    await appApi.createApplication(applicationForm)
    toast.success('API Key创建成功')
    viewApiKeys(selectedApplication.value)
  }
  catch (error) {
    toast.error('创建API Key失败')
  }
}

// 复制API Key
async function copyApiKey(key: string) {
  try {
    await navigator.clipboard.writeText(key)
    toast.success('API Key已复制到剪贴板')
  }
  catch (error) {
    toast.error('复制失败')
  }
}

// 重新生成API Key
async function regenerateApiKey(apiKey) {
  if (!confirm(`确定要重新生成应用【${apiKey.name}】的API Key吗？原Key将失效。`)) {
    return
  }

  try {
    await appApi.regenerateApiKey(selectedApplication.value.id)
    toast.success('API Key重新生成成功')
    viewApiKeys(selectedApplication.value)
  }
  catch (error) {
    toast.error('重新生成失败')
  }
}

// 删除API Key
async function deleteApiKey(apiKey) {
  if (!confirm(`确定要删除应用【${apiKey.name}】吗？`)) {
    return
  }

  try {
    await appApi.deleteApplication(selectedApplication.value.id)
    toast.success('API Key删除成功')
    viewApiKeys(selectedApplication.value)
  }
  catch (error) {
    toast.error('删除失败')
  }
}

// 关闭模态框
function closeModals() {
  showCreateModal.value = false
  showEditModal.value = false
  selectedApplication.value = null
  applicationForm.name = ''
  applicationForm.comment = ''
}

// 切换页面
function changePage(page: number) {
  pagination.page = page
  loadApplications()
}

// 监听搜索和筛选变化
watch([searchQuery, statusFilter, sortBy], () => {
  pagination.page = 1
})

// 初始化
onMounted(() => {
  loadApplications()
})
</script>

<template>
  <ClientLayout>
    <div class="space-y-6">
      <!-- 页面标题 -->
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">
            应用管理
          </h1>
          <p class="text-gray-600">
            管理您的应用和API Keys
          </p>
        </div>
        <div class="flex items-center space-x-3">
          <Button
            variant="outline"
            :disabled="loading"
            @click="loadApplications"
          >
            <RefreshCw
              class="w-4 h-4 mr-2"
              :class="[{ 'animate-spin': loading }]"
            />
            刷新
          </Button>
          <Button @click="showCreateModal = true">
            <Plus class="w-4 h-4 mr-2" />
            创建应用
          </Button>
        </div>
      </div>

      <!-- 搜索和筛选 -->
      <Card>
        <CardContent class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label class="text-sm font-medium mb-2">
                搜索应用
              </Label>
              <div class="relative">
                <Input
                  v-model="searchQuery"
                  type="text"
                  placeholder="输入应用名称或描述"
                  class="pl-10"
                />
                <Search class="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
              </div>
            </div>
            <div>
              <Label class="text-sm font-medium mb-2">
                状态筛选
              </Label>
              <Select v-model="statusFilter">
                <SelectTrigger>
                  <SelectValue placeholder="全部状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">
                    全部状态
                  </SelectItem>
                  <SelectItem value="active">
                    活跃
                  </SelectItem>
                  <SelectItem value="inactive">
                    停用
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label class="text-sm font-medium mb-2">
                排序方式
              </Label>
              <Select v-model="sortBy">
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="created_at">
                    创建时间
                  </SelectItem>
                  <SelectItem value="name">
                    应用名称
                  </SelectItem>
                  <SelectItem value="usage">
                    使用量
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 应用列表 -->
      <Card>
        <CardContent class="p-0">
          <div
            v-if="filteredApplications.length === 0 && !loading"
            class="text-center py-12"
          >
            <Layers class="mx-auto h-12 w-12 text-gray-400" />
            <h3 class="mt-2 text-sm font-medium text-gray-900">
              暂无应用
            </h3>
            <p class="mt-1 text-sm text-gray-500">
              创建您的第一个应用开始使用AI服务
            </p>
            <div class="mt-6">
              <Button @click="showCreateModal = true">
                <Plus class="w-4 h-4 mr-2" />
                创建应用
              </Button>
            </div>
          </div>

          <div
            v-else
            class="divide-y divide-gray-200"
          >
            <div
              v-for="app in filteredApplications"
              :key="app.id"
              class="p-6 hover:bg-gray-50"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Layers class="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 class="text-lg font-medium text-gray-900">
                      {{ app.name }}
                    </h3>
                    <p class="text-sm text-gray-500">
                      {{ app.description }}
                    </p>
                    <div class="flex items-center space-x-4 mt-2">
                      <Badge :variant="app.status === 'active' ? 'default' : 'destructive'">
                        {{ app.status === 'active' ? '活跃' : '停用' }}
                      </Badge>
                      <span class="text-sm text-gray-500">
                        创建于 {{ formatDate(app.created_at) }}
                      </span>
                    </div>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    @click="viewApiKeys(app)"
                  >
                    <Key class="w-4 h-4 mr-2" />
                    API Keys
                  </Button>
                  <Button
                    variant="outline"
                    @click="editApplication(app)"
                  >
                    <Edit class="w-4 h-4 mr-2" />
                    编辑
                  </Button>
                  <Button
                    variant="destructive"
                    @click="deleteApplication(app)"
                  >
                    <Trash2 class="w-4 h-4 mr-2" />
                    删除
                  </Button>
                </div>
              </div>

              <!-- 使用统计 -->
              <div class="mt-4 grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                  <CardContent class="p-3">
                    <div class="text-sm font-medium text-gray-500">
                      今日调用
                    </div>
                    <div class="text-lg font-semibold text-gray-900">
                      {{ formatNumber(app.stats?.today_calls || 0) }}
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent class="p-3">
                    <div class="text-sm font-medium text-gray-500">
                      本月调用
                    </div>
                    <div class="text-lg font-semibold text-gray-900">
                      {{ formatNumber(app.stats?.month_calls || 0) }}
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent class="p-3">
                    <div class="text-sm font-medium text-gray-500">
                      今日费用
                    </div>
                    <div class="text-lg font-semibold text-gray-900">
                      ¥{{ formatCurrency(app.stats?.today_cost || 0) }}
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent class="p-3">
                    <div class="text-sm font-medium text-gray-500">
                      本月费用
                    </div>
                    <div class="text-lg font-semibold text-gray-900">
                      ¥{{ formatCurrency(app.stats?.month_cost || 0) }}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 分页 -->
      <div
        v-if="pagination.total > pagination.pageSize"
        class="flex items-center justify-between"
      >
        <div class="text-sm text-gray-700">
          显示第 {{ (pagination.page - 1) * pagination.pageSize + 1 }} 到
          {{ Math.min(pagination.page * pagination.pageSize, pagination.total) }} 条，
          共 {{ pagination.total }} 条记录
        </div>
        <div class="flex items-center space-x-2">
          <Button
            variant="outline"
            :disabled="pagination.page <= 1"
            @click="changePage(pagination.page - 1)"
          >
            上一页
          </Button>
          <Button
            variant="outline"
            :disabled="pagination.page >= Math.ceil(pagination.total / pagination.pageSize)"
            @click="changePage(pagination.page + 1)"
          >
            下一页
          </Button>
        </div>
      </div>
    </div>

    <!-- 创建/编辑应用模态框 -->
    <Dialog
      :open="showCreateModal || showEditModal"
      @update:open="closeModals"
    >
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {{ showCreateModal ? '创建应用' : '编辑应用' }}
          </DialogTitle>
        </DialogHeader>
        <form
          class="space-y-4"
          @submit.prevent="submitApplication"
        >
          <div>
            <Label class="text-sm font-medium mb-2">
              应用名称
            </Label>
            <Input
              v-model="applicationForm.name"
              type="text"
              required
              placeholder="输入应用名称"
            />
          </div>
          <div>
            <Label class="text-sm font-medium mb-2">
              应用备注
            </Label>
            <Textarea
              v-model="applicationForm.comment"
              rows="3"
              placeholder="输入应用备注（可选）"
            />
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              @click="closeModals"
            >
              取消
            </Button>
            <Button
              type="submit"
              :disabled="submitting"
            >
              {{ submitting ? '保存中...' : '保存' }}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>

    <!-- API Keys模态框 -->
    <Dialog
      :open="showApiKeysModal"
      @update:open="(open) => showApiKeysModal = open"
    >
      <DialogContent class="max-w-4xl">
        <DialogHeader>
          <div class="flex items-center justify-between">
            <DialogTitle>
              {{ selectedApplication?.name }} - API Keys
            </DialogTitle>
            <Button @click="createApiKey">
              <Plus class="w-4 h-4 mr-2" />
              创建API Key
            </Button>
          </div>
        </DialogHeader>
        <div class="space-y-3">
          <Card
            v-for="apiKey in apiKeys"
            :key="apiKey.id"
          >
            <CardContent class="p-4">
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="font-medium text-gray-900">
                    {{ apiKey.name }}
                  </h4>
                  <p class="text-sm text-gray-500">
                    {{ apiKey.description }}
                  </p>
                  <div class="flex items-center space-x-4 mt-2">
                    <Badge :variant="apiKey.status === 'active' ? 'default' : 'destructive'">
                      {{ apiKey.status === 'active' ? '活跃' : '停用' }}
                    </Badge>
                    <span class="text-sm text-gray-500">
                      创建于 {{ formatDate(apiKey.created_at) }}
                    </span>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    @click="copyApiKey(apiKey.key)"
                  >
                    <Copy class="w-4 h-4 mr-2" />
                    复制
                  </Button>
                  <Button
                    variant="outline"
                    @click="regenerateApiKey(apiKey)"
                  >
                    <RotateCcw class="w-4 h-4 mr-2" />
                    重新生成
                  </Button>
                  <Button
                    variant="destructive"
                    @click="deleteApiKey(apiKey)"
                  >
                    <Trash2 class="w-4 h-4 mr-2" />
                    删除
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  </ClientLayout>
</template>
