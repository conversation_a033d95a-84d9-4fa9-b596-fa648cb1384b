<script setup lang="ts">
import {
  Badge,
  Button,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@billing/ui'
import {
  ArcElement,
  CategoryScale,
  Chart as ChartJS,
  Legend,
  LinearScale,
  LineElement,
  PointElement,
  Title,
  Tooltip,
} from 'chart.js'
import {
  Activity,
  DollarSign,
  Download,
  RefreshCw,
  TrendingUp,
  Zap,
} from 'lucide-vue-next'
import { computed, onMounted, reactive, ref, watch } from 'vue'
import {
  Doughnut,
  Line,
} from 'vue-chartjs'
import { toast } from 'vue-sonner'
import { appApi, billingApi } from '@/api'
import ClientLayout from '@/layouts/ClientLayout.vue'

// 注册Chart.js组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
)

const loading = ref(false)
const timeRange = ref('30d')
const customStartDate = ref('')
const customEndDate = ref('')
const selectedApplication = ref('')
const trendMetric = ref('calls')

// 应用列表
const applications = ref([])

// 统计数据
const billingStats = reactive({
  totalCalls: 0,
  totalTokens: 0,
  totalCost: 0,
  avgCostPerCall: 0,
})

// 趋势数据
const trendData = reactive({
  labels: [],
  datasets: [{ data: [] }],
})

// 服务分布数据
const serviceData = reactive({
  labels: [],
  datasets: [{ data: [] }],
})

// 详细记录
const billingRecords = ref([])
const recordPagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0,
})

// 图表配置
const trendChartData = computed(() => ({
  labels: trendData.labels,
  datasets: [
    {
      label: getTrendLabel(),
      data: trendData.datasets[0]?.data || [],
      borderColor: 'rgb(59, 130, 246)',
      backgroundColor: 'rgba(59, 130, 246, 0.1)',
      tension: 0.4,
    },
  ],
}))

const trendChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false,
    },
  },
  scales: {
    y: {
      beginAtZero: true,
    },
  },
}

const serviceChartData = computed(() => ({
  labels: serviceData.labels,
  datasets: [
    {
      data: serviceData.datasets[0]?.data || [],
      backgroundColor: [
        'rgba(59, 130, 246, 0.8)',
        'rgba(16, 185, 129, 0.8)',
        'rgba(139, 92, 246, 0.8)',
        'rgba(245, 158, 11, 0.8)',
      ],
      borderWidth: 0,
    },
  ],
}))

const serviceChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom' as const,
    },
  },
} as any

// 获取趋势图标签
function getTrendLabel() {
  switch (trendMetric.value) {
    case 'calls':
      return '调用次数'
    case 'tokens':
      return 'Token使用量'
    case 'cost':
      return '费用 (¥)'
    default:
      return ''
  }
}

// 格式化数字
function formatNumber(num: number) {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`
  }
  if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`
  }
  return num.toString()
}

// 格式化货币
function formatCurrency(amount: number) {
  return amount.toFixed(2)
}

// 格式化日期时间
function formatDateTime(dateString: string) {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 获取服务类型名称
function getServiceTypeName(type: string) {
  const names = {
    llm: 'LLM',
    tts: 'TTS',
    asr: 'ASR',
  }
  return names[type] || type
}

// 获取服务类型颜色
function getServiceTypeColor(type: string) {
  const colors = {
    llm: 'bg-blue-100 text-blue-800',
    tts: 'bg-green-100 text-green-800',
    asr: 'bg-purple-100 text-purple-800',
  }
  return colors[type] || 'bg-gray-100 text-gray-800'
}

// 时间范围变化
function onTimeRangeChange() {
  if (timeRange.value !== 'custom') {
    loadBillingData()
  }
}

// 加载应用列表
async function loadApplications() {
  try {
    const data = await appApi.getList({
      page: 1,
      page_size: 10,
    })
    applications.value = Array.isArray(data) ? data : []
  }
  catch (error) {
    console.error('Failed to load applications:', error)
  }
}

// 加载统计数据
async function loadBillingStats() {
  try {
    const data = await billingApi.getBillingStats()
    Object.assign(billingStats, data || {})
  }
  catch (error) {
    console.error('Failed to load billing stats:', error)
  }
}

// 加载趋势数据
async function loadTrendData() {
  try {
    const params = {
      ...getQueryParams(),
      metric: trendMetric.value,
    }
    const data = await billingApi.getUsageTrends(params)
    Object.assign(trendData, data)
  }
  catch (error) {
    console.error('Failed to load trend data:', error)
  }
}

// 加载服务分布数据
async function loadServiceData() {
  try {
    const params = getQueryParams()
    const data = await billingApi.getServiceUsage(params)
    Object.assign(serviceData, data)
  }
  catch (error) {
    console.error('Failed to load service data:', error)
  }
}

// 加载详细记录
async function loadBillingRecords() {
  try {
    const params = {
      ...getQueryParams(),
      page: recordPagination.page,
      pageSize: recordPagination.pageSize,
    }
    const data = await billingApi.getBillingPeriods(params)
    billingRecords.value = Array.isArray(data) ? data : []
    recordPagination.total = Array.isArray(data) ? data.length : 0
  }
  catch (error) {
    console.error('Failed to load billing records:', error)
  }
}

// 获取查询参数
function getQueryParams() {
  const params: any = {}

  if (timeRange.value === 'custom') {
    if (customStartDate.value)
      params.startDate = customStartDate.value
    if (customEndDate.value)
      params.endDate = customEndDate.value
  }
  else {
    params.period = timeRange.value
  }

  if (selectedApplication.value) {
    params.applicationId = selectedApplication.value
  }

  return params
}

// 加载所有计费数据
async function loadBillingData() {
  loading.value = true
  try {
    await Promise.all([
      loadBillingStats(),
      loadTrendData(),
      loadServiceData(),
      loadBillingRecords(),
    ])
  }
  catch (error) {
    toast.error('加载数据失败')
  }
  finally {
    loading.value = false
  }
}

// 刷新数据
function refreshData() {
  loadBillingData()
}

// 导出报告
async function exportReport() {
  try {
    const params = getQueryParams()
    await billingApi.exportBillingReport(params)
    toast.success('报告导出成功')
  }
  catch (error) {
    toast.error('导出失败')
  }
}

// 切换记录页面
function changeRecordPage(page: number) {
  recordPagination.page = page
  loadBillingRecords()
}

// 监听趋势指标变化
watch(trendMetric, () => {
  loadTrendData()
})

// 监听自定义日期变化
watch([customStartDate, customEndDate], () => {
  if (timeRange.value === 'custom' && customStartDate.value && customEndDate.value) {
    loadBillingData()
  }
})

// 初始化
onMounted(() => {
  loadApplications()
  loadBillingData()
})
</script>

<template>
  <ClientLayout>
    <div class="space-y-6">
      <!-- 页面标题 -->
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">
            计费统计
          </h1>
          <p class="text-gray-600">
            查看详细的使用统计和费用分析
          </p>
        </div>
        <div class="flex items-center space-x-3">
          <Button
            variant="outline"
            @click="exportReport"
          >
            <Download class="w-4 h-4 mr-2" />
            导出报告
          </Button>
          <Button
            variant="outline"
            :disabled="loading"
            @click="refreshData"
          >
            <RefreshCw
              class="w-4 h-4 mr-2"
              :class="[{ 'animate-spin': loading }]"
            />
            刷新
          </Button>
        </div>
      </div>

      <!-- 时间范围选择 -->
      <Card>
        <CardContent class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label class="block text-sm font-medium mb-2">
                时间范围
              </Label>
              <Select
                v-model="timeRange"
                @update:model-value="onTimeRangeChange"
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="today">
                    今天
                  </SelectItem>
                  <SelectItem value="7d">
                    最近7天
                  </SelectItem>
                  <SelectItem value="30d">
                    最近30天
                  </SelectItem>
                  <SelectItem value="90d">
                    最近90天
                  </SelectItem>
                  <SelectItem value="custom">
                    自定义
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div v-if="timeRange === 'custom'">
              <Label class="block text-sm font-medium mb-2">
                开始日期
              </Label>
              <Input
                v-model="customStartDate"
                type="date"
              />
            </div>
            <div v-if="timeRange === 'custom'">
              <Label class="block text-sm font-medium mb-2">
                结束日期
              </Label>
              <Input
                v-model="customEndDate"
                type="date"
              />
            </div>
            <div>
              <Label class="block text-sm font-medium mb-2">
                应用筛选
              </Label>
              <Select
                v-model="selectedApplication"
                @update:model-value="loadBillingData"
              >
                <SelectTrigger>
                  <SelectValue placeholder="全部应用" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">
                    全部应用
                  </SelectItem>
                  <SelectItem
                    v-for="app in applications"
                    :key="app.id"
                    :value="app.id"
                  >
                    {{ app.name }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 统计概览 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                  <Activity class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">
                    总调用次数
                  </dt>
                  <dd class="text-lg font-medium text-gray-900">
                    {{ formatNumber(billingStats.totalCalls) }}
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                  <Zap class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">
                    总Token使用
                  </dt>
                  <dd class="text-lg font-medium text-gray-900">
                    {{ formatNumber(billingStats.totalTokens) }}
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                  <DollarSign class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">
                    总费用
                  </dt>
                  <dd class="text-lg font-medium text-gray-900">
                    ¥{{ formatCurrency(billingStats.totalCost) }}
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                  <TrendingUp class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">
                    平均单价
                  </dt>
                  <dd class="text-lg font-medium text-gray-900">
                    ¥{{ formatCurrency(billingStats.avgCostPerCall) }}
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- 图表区域 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>使用趋势</CardTitle>
            <CardDescription>过去一段时间的使用变化</CardDescription>
          </CardHeader>
          <CardContent>
            <div class="flex items-center space-x-2 mb-4">
              <Button
                :variant="trendMetric === 'calls' ? 'default' : 'ghost'"
                size="sm"
                @click="trendMetric = 'calls'"
              >
                调用次数
              </Button>
              <Button
                :variant="trendMetric === 'tokens' ? 'default' : 'ghost'"
                size="sm"
                @click="trendMetric = 'tokens'"
              >
                Token使用
              </Button>
              <Button
                :variant="trendMetric === 'cost' ? 'default' : 'ghost'"
                size="sm"
                @click="trendMetric = 'cost'"
              >
                费用
              </Button>
            </div>
            <div class="h-64">
              <Line
                v-if="trendData.labels.length > 0"
                :data="trendChartData"
                :options="trendChartOptions"
              />
              <div
                v-else
                class="flex items-center justify-center h-full text-gray-500"
              >
                暂无数据
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>服务使用分布</CardTitle>
            <CardDescription>各服务类型的使用占比</CardDescription>
          </CardHeader>
          <CardContent>
            <div class="h-64">
              <Doughnut
                v-if="serviceData.labels.length > 0"
                :data="serviceChartData"
                :options="serviceChartOptions"
              />
              <div
                v-else
                class="flex items-center justify-center h-full text-gray-500"
              >
                暂无数据
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- 详细数据表格 -->
      <Card>
        <CardHeader>
          <CardTitle>详细记录</CardTitle>
          <CardDescription>查看所有的计费记录详情</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>时间</TableHead>
                <TableHead>应用</TableHead>
                <TableHead>服务类型</TableHead>
                <TableHead>调用次数</TableHead>
                <TableHead>Token使用</TableHead>
                <TableHead>费用</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow
                v-for="record in billingRecords"
                :key="record.id"
              >
                <TableCell>{{ formatDateTime(record.date) }}</TableCell>
                <TableCell>{{ record.application_name }}</TableCell>
                <TableCell>
                  <Badge
                    :class="[
                      getServiceTypeColor(record.service_type),
                    ]"
                  >
                    {{ getServiceTypeName(record.service_type) }}
                  </Badge>
                </TableCell>
                <TableCell>{{ formatNumber(record.calls) }}</TableCell>
                <TableCell>{{ formatNumber(record.tokens) }}</TableCell>
                <TableCell>¥{{ formatCurrency(record.cost) }}</TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <!-- 分页 -->
      <div
        v-if="recordPagination.total > recordPagination.pageSize"
        class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6"
      >
        <div class="flex-1 flex justify-between sm:hidden">
          <Button
            variant="outline"
            :disabled="recordPagination.page <= 1"
            @click="changeRecordPage(recordPagination.page - 1)"
          >
            上一页
          </Button>
          <Button
            variant="outline"
            :disabled="recordPagination.page >= Math.ceil(recordPagination.total / recordPagination.pageSize)"
            @click="changeRecordPage(recordPagination.page + 1)"
          >
            下一页
          </Button>
        </div>
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p class="text-sm text-gray-700">
              显示第
              <span class="font-medium">{{ (recordPagination.page - 1) * recordPagination.pageSize + 1 }}</span>
              到
              <span class="font-medium">{{ Math.min(recordPagination.page * recordPagination.pageSize, recordPagination.total) }}</span>
              条，共
              <span class="font-medium">{{ recordPagination.total }}</span>
              条记录
            </p>
          </div>
          <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
              <Button
                variant="outline"
                :disabled="recordPagination.page <= 1"
                @click="changeRecordPage(recordPagination.page - 1)"
              >
                上一页
              </Button>
              <Button
                variant="outline"
                :disabled="recordPagination.page >= Math.ceil(recordPagination.total / recordPagination.pageSize)"
                @click="changeRecordPage(recordPagination.page + 1)"
              >
                下一页
              </Button>
            </nav>
          </div>
        </div>
      </div>
    </div>
  </ClientLayout>
</template>
