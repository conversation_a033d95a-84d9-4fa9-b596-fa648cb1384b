<script setup lang="ts">
import {
  Bell,
  ChevronDown,
  CreditCard,
  FileText,
  Folder,
  LayoutDashboard,
  LogOut,
  Menu,
  Settings,
  User,
} from 'lucide-vue-next'
import { onMounted, onUnmounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { toast } from 'vue-sonner'
import { authApi } from '@/api'
import { useUserStore } from '@/store'

const router = useRouter()
const userStore = useUserStore()

// 导航菜单
const navigation = [
  { name: '控制台', href: '/dashboard', icon: LayoutDashboard },
  { name: '应用管理', href: '/applications', icon: Folder },
  { name: '计费查看', href: '/billing', icon: CreditCard },
  { name: '文档', href: '/docs', icon: FileText },
]

// 菜单状态
const showUserMenu = ref(false)
const showMobileMenu = ref(false)
const userMenuRef = ref<HTMLElement>()

// 切换用户菜单
function toggleUserMenu() {
  showUserMenu.value = !showUserMenu.value
}

// 切换移动端菜单
function toggleMobileMenu() {
  showMobileMenu.value = !showMobileMenu.value
}

// 处理登出
async function handleLogout() {
  try {
    await authApi.logout()
    toast.success('已成功退出登录')
    router.push('/auth/login')
  }
  catch (error) {
    toast.error('退出登录失败')
  }
}

// 点击外部关闭菜单
function handleClickOutside(event: Event) {
  if (userMenuRef.value && !userMenuRef.value.contains(event.target as Node)) {
    showUserMenu.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 顶部导航 -->
    <header class="bg-white shadow-sm border-b">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo -->
          <div class="flex items-center">
            <RouterLink
              to="/dashboard"
              class="flex items-center space-x-2"
            >
              <img
                src="/logo.png"
                alt="logo"
                class="w-14 h-14 mt-2"
              >
              <span class="text-xl font-semibold text-gray-900">柚子 AI</span>
            </RouterLink>
          </div>

          <!-- 主导航 -->
          <nav class="hidden md:flex space-x-8">
            <RouterLink
              v-for="item in navigation"
              :key="item.name"
              :to="item.href"
              class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors"
              :class="{
                'text-blue-600 bg-blue-50': $route.path.startsWith(item.href),
              }"
            >
              <component
                :is="item.icon"
                class="w-4 h-4 inline-block mr-2"
              />
              {{ item.name }}
            </RouterLink>
          </nav>

          <!-- 用户菜单 -->
          <div class="flex items-center space-x-4">
            <!-- 通知 -->
            <button class="text-gray-400 hover:text-gray-600 transition-colors">
              <Bell class="w-5 h-5" />
            </button>

            <!-- 用户下拉菜单 -->
            <div
              ref="userMenuRef"
              class="relative"
            >
              <button
                class="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
                @click="toggleUserMenu"
              >
                <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                  <User class="w-4 h-4" />
                </div>
                <span class="hidden md:block text-sm font-medium">{{ userStore.user?.name || '用户' }}</span>
                <ChevronDown class="w-4 h-4" />
              </button>

              <!-- 下拉菜单 -->
              <div
                v-show="showUserMenu"
                class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border"
              >
                <RouterLink
                  to="/profile"
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                >
                  <Settings class="w-4 h-4 inline-block mr-2" />
                  个人设置
                </RouterLink>
                <button
                  class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                  @click="handleLogout"
                >
                  <LogOut class="w-4 h-4 inline-block mr-2" />
                  退出登录
                </button>
              </div>
            </div>

            <!-- 移动端菜单按钮 -->
            <button
              class="md:hidden text-gray-600 hover:text-gray-900 transition-colors"
              @click="toggleMobileMenu"
            >
              <Menu class="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>

      <!-- 移动端导航 -->
      <div
        v-show="showMobileMenu"
        class="md:hidden border-t bg-white"
      >
        <div class="px-2 pt-2 pb-3 space-y-1">
          <RouterLink
            v-for="item in navigation"
            :key="item.name"
            :to="item.href"
            class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-colors"
            :class="{
              'text-blue-600 bg-blue-50': $route.path.startsWith(item.href),
            }"
            @click="showMobileMenu = false"
          >
            <component
              :is="item.icon"
              class="w-4 h-4 inline-block mr-2"
            />
            {{ item.name }}
          </RouterLink>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <slot />
    </main>
  </div>
</template>
