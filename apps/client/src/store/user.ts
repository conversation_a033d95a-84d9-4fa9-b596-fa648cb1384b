import type { User } from '@billing/common'
import { defineStore } from 'pinia'
import { computed, ref, watch } from 'vue'
import { authApi } from '@/api/auth'

export const useUserStore = defineStore('user', () => {
  const token = ref('')
  const user = ref<User | null>(null)

  // 从localStorage恢复token
  const savedToken = localStorage.getItem('auth_token')
  if (savedToken) {
    token.value = savedToken
  }

  const setToken = (t: string) => {
    token.value = t
  }

  const setUser = (u: User) => {
    user.value = u
  }

  const reset = () => {
    token.value = ''
    user.value = null
    localStorage.removeItem('auth_token')
  }

  // 检查是否已登录
  const isLoggedIn = computed(() => {
    return !!token.value
  })

  return {
    token,
    user,
    setToken,
    setUser,
    reset,
    isLoggedIn,
  }
}, {
  persist: true,
})
